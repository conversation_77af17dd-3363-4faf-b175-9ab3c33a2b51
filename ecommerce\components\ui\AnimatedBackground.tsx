"use client";

import React from 'react';
import { motion } from 'framer-motion';

interface AnimatedBackgroundProps {
  variant?: 'hero' | 'section' | 'minimal';
  className?: string;
  children?: React.ReactNode;
}

export default function AnimatedBackground({ 
  variant = 'hero', 
  className = '',
  children 
}: AnimatedBackgroundProps) {
  
  const getVariantConfig = () => {
    switch (variant) {
      case 'hero':
        return {
          gradients: [
            {
              className: "absolute inset-0 bg-gradient-to-br from-purple-500/15 via-blue-400/10 to-teal-400/15",
              animate: { opacity: [0.3, 0.6, 0.3] },
              transition: { duration: 8, repeat: Infinity, ease: "easeInOut" }
            },
            {
              className: "absolute inset-0 bg-gradient-to-tl from-teal-300/10 via-transparent to-purple-500/8",
              animate: { opacity: [0.2, 0.5, 0.2] },
              transition: { duration: 12, repeat: Infinity, ease: "easeInOut", delay: 2 }
            }
          ],
          orbs: [
            {
              className: "absolute top-[5%] right-[-5%] w-[600px] h-[600px] rounded-full bg-gradient-to-br from-purple-400/25 to-blue-400/15 blur-3xl",
              animate: {
                scale: [1, 1.2, 1],
                opacity: [0.3, 0.6, 0.3],
                x: [0, 50, 0],
                y: [0, -30, 0]
              },
              transition: { duration: 12, repeat: Infinity, ease: "easeInOut" }
            },
            {
              className: "absolute top-[30%] left-[-8%] w-[700px] h-[700px] rounded-full bg-gradient-to-tr from-blue-400/20 to-teal-400/15 blur-3xl",
              animate: {
                scale: [1, 0.8, 1],
                opacity: [0.3, 0.6, 0.3],
                x: [0, -40, 0],
                y: [0, 40, 0]
              },
              transition: { duration: 15, repeat: Infinity, ease: "easeInOut", delay: 2 }
            },
            {
              className: "absolute bottom-[0%] right-[10%] w-[500px] h-[500px] rounded-full bg-gradient-to-bl from-teal-400/20 to-purple-400/15 blur-3xl",
              animate: {
                scale: [1, 1.3, 1],
                opacity: [0.35, 0.65, 0.35],
                x: [0, -20, 0],
                y: [0, 20, 0]
              },
              transition: { duration: 10, repeat: Infinity, ease: "easeInOut", delay: 4 }
            },
            {
              className: "absolute top-[60%] left-[20%] w-[400px] h-[400px] rounded-full bg-gradient-to-tr from-yellow-500/20 to-orange-500/15 blur-3xl",
              animate: {
                scale: [1, 1.1, 1],
                opacity: [0.25, 0.5, 0.25],
                x: [0, -20, 0],
                y: [0, 15, 0]
              },
              transition: { duration: 14, repeat: Infinity, ease: "easeInOut", delay: 6 }
            }
          ],
          particles: [
            {
              className: "absolute top-[15%] left-[15%] w-3 h-3 bg-theme-accent-primary/40 rounded-full",
              animate: { 
                y: [0, -20, 0],
                opacity: [0.4, 0.8, 0.4],
                scale: [1, 1.2, 1]
              },
              transition: { duration: 6, repeat: Infinity, ease: "easeInOut" }
            },
            {
              className: "absolute top-[45%] right-[25%] w-4 h-4 bg-theme-accent-secondary/50 rounded-full",
              animate: { 
                y: [0, -15, 0],
                x: [0, 10, 0],
                opacity: [0.5, 0.9, 0.5]
              },
              transition: { duration: 8, repeat: Infinity, ease: "easeInOut", delay: 1 }
            },
            {
              className: "absolute top-[70%] left-[35%] w-2 h-2 bg-blue-400/60 rounded-full",
              animate: { 
                y: [0, -25, 0],
                opacity: [0.6, 1, 0.6]
              },
              transition: { duration: 7, repeat: Infinity, ease: "easeInOut", delay: 2 }
            },
            {
              className: "absolute top-[25%] right-[55%] w-3.5 h-3.5 bg-purple-400/50 rounded-full",
              animate: { 
                y: [0, -18, 0],
                x: [0, -8, 0],
                opacity: [0.5, 0.85, 0.5]
              },
              transition: { duration: 9, repeat: Infinity, ease: "easeInOut", delay: 3 }
            }
          ],
          geometric: [
            {
              className: "absolute top-[10%] left-[5%] w-24 h-24 border border-theme-accent-primary/25",
              animate: { 
                rotate: [0, 360],
                scale: [1, 1.1, 1],
                opacity: [0.25, 0.5, 0.25]
              },
              transition: { duration: 20, repeat: Infinity, ease: "linear" }
            },
            {
              className: "absolute bottom-[15%] right-[10%] w-20 h-20 border border-theme-accent-secondary/25",
              animate: { 
                rotate: [360, 0],
                scale: [1, 0.9, 1],
                opacity: [0.25, 0.45, 0.25]
              },
              transition: { duration: 25, repeat: Infinity, ease: "linear" }
            }
          ]
        };
      case 'section':
        return {
          gradients: [
            { 
              className: "absolute inset-0 bg-gradient-to-r from-white/10 via-white/5 to-white/10",
              animate: { opacity: [0.1, 0.2, 0.1] },
              transition: { duration: 6, repeat: Infinity, ease: "easeInOut" }
            }
          ],
          orbs: [
            {
              className: "absolute top-[-20%] right-[-20%] w-[300px] h-[300px] rounded-full bg-gradient-to-br from-theme-accent-primary/15 to-theme-accent-secondary/10 blur-2xl",
              animate: { 
                scale: [1, 1.1, 1],
                opacity: [0.15, 0.3, 0.15]
              },
              transition: { duration: 8, repeat: Infinity, ease: "easeInOut" }
            },
            {
              className: "absolute bottom-[-20%] left-[-20%] w-[250px] h-[250px] rounded-full bg-gradient-to-tr from-blue-400/10 to-purple-400/10 blur-2xl",
              animate: { 
                scale: [1, 0.9, 1],
                opacity: [0.1, 0.25, 0.1]
              },
              transition: { duration: 10, repeat: Infinity, ease: "easeInOut", delay: 2 }
            }
          ],
          particles: [
            {
              className: "absolute top-[20%] left-[20%] w-1.5 h-1.5 bg-theme-accent-primary/30 rounded-full",
              animate: { 
                y: [0, -10, 0],
                opacity: [0.3, 0.6, 0.3]
              },
              transition: { duration: 5, repeat: Infinity, ease: "easeInOut" }
            },
            {
              className: "absolute bottom-[30%] right-[30%] w-2 h-2 bg-theme-accent-secondary/40 rounded-full",
              animate: { 
                y: [0, -8, 0],
                opacity: [0.4, 0.7, 0.4]
              },
              transition: { duration: 6, repeat: Infinity, ease: "easeInOut", delay: 1 }
            }
          ],
          geometric: []
        };
      default:
        return {
          gradients: [
            { 
              className: "absolute inset-0 bg-gradient-to-br from-gray-50/20 to-white/10",
              animate: { opacity: [0.1, 0.15, 0.1] },
              transition: { duration: 4, repeat: Infinity, ease: "easeInOut" }
            }
          ],
          orbs: [
            {
              className: "absolute top-[10%] right-[-10%] w-[200px] h-[200px] rounded-full bg-gradient-to-br from-theme-accent-primary/10 to-theme-accent-secondary/5 blur-xl",
              animate: { 
                scale: [1, 1.05, 1],
                opacity: [0.1, 0.2, 0.1]
              },
              transition: { duration: 6, repeat: Infinity, ease: "easeInOut" }
            }
          ],
          particles: [],
          geometric: []
        };
    }
  };

  const config = getVariantConfig();

  return (
    <div className={`relative overflow-hidden ${className}`}>
      {/* Animated gradients */}
      {config.gradients.map((gradient, index) => (
        <motion.div
          key={`gradient-${index}`}
          className={gradient.className}
          animate={gradient.animate}
          transition={gradient.transition}
        />
      ))}
      
      {/* Animated orbs */}
      {config.orbs.map((orb, index) => (
        <motion.div
          key={`orb-${index}`}
          className={orb.className}
          animate={orb.animate}
          transition={orb.transition}
        />
      ))}
      
      {/* Floating particles */}
      {config.particles.map((particle, index) => (
        <motion.div
          key={`particle-${index}`}
          className={particle.className}
          animate={particle.animate}
          transition={particle.transition}
        />
      ))}
      
      {/* Geometric shapes */}
      {config.geometric.map((shape, index) => (
        <motion.div
          key={`shape-${index}`}
          className={shape.className}
          animate={shape.animate}
          transition={shape.transition}
        />
      ))}
      
      {/* Subtle grid pattern */}
      <div className="absolute inset-0 opacity-[0.02] bg-[radial-gradient(#000_1px,transparent_1px)] bg-[size:50px_50px]"></div>
      
      {/* Content */}
      <div className="relative z-10">
        {children}
      </div>
    </div>
  );
}
