"use client";

import React from 'react';
import { motion } from 'framer-motion';

interface GradientOverlayProps {
  variant?: 'cosmic' | 'ocean' | 'sunset' | 'emerald' | 'vibrant' | 'aurora';
  className?: string;
  children?: React.ReactNode;
  intensity?: 'light' | 'medium' | 'strong';
}

export default function GradientOverlay({ 
  variant = 'cosmic', 
  className = '',
  children,
  intensity = 'medium'
}: GradientOverlayProps) {
  
  const getGradientConfig = () => {
    const intensityMap = {
      light: { opacity: 0.3, blur: 'blur-sm' },
      medium: { opacity: 0.5, blur: 'blur-md' },
      strong: { opacity: 0.7, blur: 'blur-lg' }
    };
    
    const config = intensityMap[intensity];
    
    switch (variant) {
      case 'cosmic':
        return {
          gradients: [
            `linear-gradient(135deg, rgba(255, 154, 158, ${config.opacity}) 0%, rgba(254, 207, 239, ${config.opacity * 0.8}) 25%, rgba(168, 237, 234, ${config.opacity}) 75%, rgba(254, 214, 227, ${config.opacity * 0.9}) 100%)`,
            `radial-gradient(circle at 20% 80%, rgba(120, 119, 198, ${config.opacity * 0.6}) 0%, transparent 50%)`,
            `radial-gradient(circle at 80% 20%, rgba(255, 119, 198, ${config.opacity * 0.5}) 0%, transparent 50%)`
          ],
          animation: 'gradient-shift 20s ease infinite',
          blur: config.blur
        };
      
      case 'ocean':
        return {
          gradients: [
            `linear-gradient(135deg, rgba(102, 126, 234, ${config.opacity}) 0%, rgba(118, 75, 162, ${config.opacity * 0.8}) 25%, rgba(107, 115, 255, ${config.opacity}) 50%, rgba(154, 154, 255, ${config.opacity * 0.9}) 100%)`,
            `radial-gradient(circle at 40% 40%, rgba(0, 13, 255, ${config.opacity * 0.4}) 0%, transparent 50%)`
          ],
          animation: 'gradient-shift 18s ease infinite',
          blur: config.blur
        };
      
      case 'sunset':
        return {
          gradients: [
            `linear-gradient(135deg, rgba(247, 112, 154, ${config.opacity}) 0%, rgba(254, 225, 64, ${config.opacity * 0.8}) 50%, rgba(247, 112, 154, ${config.opacity}) 100%)`,
            `radial-gradient(circle at 60% 30%, rgba(255, 107, 107, ${config.opacity * 0.5}) 0%, transparent 50%)`
          ],
          animation: 'gradient-shift 22s ease infinite',
          blur: config.blur
        };
      
      case 'emerald':
        return {
          gradients: [
            `linear-gradient(135deg, rgba(17, 153, 142, ${config.opacity}) 0%, rgba(56, 239, 125, ${config.opacity * 0.8}) 50%, rgba(17, 153, 142, ${config.opacity}) 100%)`,
            `radial-gradient(circle at 30% 70%, rgba(46, 204, 113, ${config.opacity * 0.6}) 0%, transparent 50%)`
          ],
          animation: 'gradient-shift 16s ease infinite',
          blur: config.blur
        };
      
      case 'vibrant':
        return {
          gradients: [
            `linear-gradient(135deg, rgba(102, 126, 234, ${config.opacity}) 0%, rgba(118, 75, 162, ${config.opacity * 0.7}) 25%, rgba(240, 147, 251, ${config.opacity * 0.8}) 50%, rgba(245, 87, 108, ${config.opacity * 0.6}) 75%, rgba(79, 172, 254, ${config.opacity}) 100%)`,
            `radial-gradient(circle at 25% 25%, rgba(255, 107, 107, ${config.opacity * 0.4}) 0%, transparent 50%)`,
            `radial-gradient(circle at 75% 75%, rgba(107, 255, 107, ${config.opacity * 0.3}) 0%, transparent 50%)`
          ],
          animation: 'gradient-shift 25s ease infinite',
          blur: config.blur
        };
      
      case 'aurora':
        return {
          gradients: [
            `linear-gradient(45deg, rgba(102, 126, 234, ${config.opacity}) 0%, rgba(118, 75, 162, ${config.opacity * 0.8}) 50%, rgba(102, 126, 234, ${config.opacity}) 100%)`,
            `radial-gradient(circle at 50% 0%, rgba(147, 51, 234, ${config.opacity * 0.5}) 0%, transparent 50%)`
          ],
          animation: 'gradient-shift 15s ease infinite',
          blur: config.blur
        };
      
      default:
        return {
          gradients: [
            `linear-gradient(135deg, rgba(255, 154, 158, ${config.opacity}) 0%, rgba(254, 207, 239, ${config.opacity}) 100%)`
          ],
          animation: 'gradient-shift 20s ease infinite',
          blur: config.blur
        };
    }
  };

  const config = getGradientConfig();

  return (
    <div className={`relative overflow-hidden ${className}`}>
      {/* Gradient Overlays */}
      {config.gradients.map((gradient, index) => (
        <motion.div
          key={index}
          className={`absolute inset-0 ${config.blur}`}
          style={{
            background: gradient,
            backgroundSize: '400% 400%',
            animation: config.animation
          }}
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ 
            duration: 2, 
            delay: index * 0.5,
            ease: "easeOut" 
          }}
        />
      ))}
      
      {/* Content */}
      {children && (
        <div className="relative z-10">
          {children}
        </div>
      )}
    </div>
  );
}

// Preset components for easy use
export function CosmicGradient({ 
  children, 
  className = '', 
  intensity = 'medium' 
}: { 
  children?: React.ReactNode; 
  className?: string; 
  intensity?: 'light' | 'medium' | 'strong';
}) {
  return (
    <GradientOverlay variant="cosmic" intensity={intensity} className={className}>
      {children}
    </GradientOverlay>
  );
}

export function OceanGradient({ 
  children, 
  className = '', 
  intensity = 'medium' 
}: { 
  children?: React.ReactNode; 
  className?: string; 
  intensity?: 'light' | 'medium' | 'strong';
}) {
  return (
    <GradientOverlay variant="ocean" intensity={intensity} className={className}>
      {children}
    </GradientOverlay>
  );
}

export function SunsetGradient({ 
  children, 
  className = '', 
  intensity = 'medium' 
}: { 
  children?: React.ReactNode; 
  className?: string; 
  intensity?: 'light' | 'medium' | 'strong';
}) {
  return (
    <GradientOverlay variant="sunset" intensity={intensity} className={className}>
      {children}
    </GradientOverlay>
  );
}

export function VibrantGradient({ 
  children, 
  className = '', 
  intensity = 'medium' 
}: { 
  children?: React.ReactNode; 
  className?: string; 
  intensity?: 'light' | 'medium' | 'strong';
}) {
  return (
    <GradientOverlay variant="vibrant" intensity={intensity} className={className}>
      {children}
    </GradientOverlay>
  );
}
