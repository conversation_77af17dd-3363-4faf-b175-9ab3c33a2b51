"use client";

import React, { useRef } from 'react';
import { motion, useInView, Variants } from 'framer-motion';

interface ScrollRevealProps {
  children: React.ReactNode;
  className?: string;
  animation?: 'fadeIn' | 'slideUp' | 'slideDown' | 'slideLeft' | 'slideRight' | 'scaleIn' | 'rotateIn';
  delay?: number;
  duration?: number;
  threshold?: number;
  once?: boolean;
  stagger?: number;
  glassEffect?: boolean;
  hoverEffect?: boolean;
}

const animationVariants: Record<string, Variants> = {
  fadeIn: {
    hidden: { opacity: 0 },
    visible: { opacity: 1 }
  },
  slideUp: {
    hidden: { opacity: 0, y: 50 },
    visible: { opacity: 1, y: 0 }
  },
  slideDown: {
    hidden: { opacity: 0, y: -50 },
    visible: { opacity: 1, y: 0 }
  },
  slideLeft: {
    hidden: { opacity: 0, x: 50 },
    visible: { opacity: 1, x: 0 }
  },
  slideRight: {
    hidden: { opacity: 0, x: -50 },
    visible: { opacity: 1, x: 0 }
  },
  scaleIn: {
    hidden: { opacity: 0, scale: 0.8 },
    visible: { opacity: 1, scale: 1 }
  },
  rotateIn: {
    hidden: { opacity: 0, rotate: -10, scale: 0.9 },
    visible: { opacity: 1, rotate: 0, scale: 1 }
  }
};

export default function ScrollReveal({
  children,
  className = '',
  animation = 'fadeIn',
  delay = 0,
  duration = 0.6,
  threshold = 0.1,
  once = true,
  stagger = 0,
  glassEffect = false,
  hoverEffect = false
}: ScrollRevealProps) {
  const ref = useRef(null);
  const isInView = useInView(ref, {
    amount: threshold,
    once,
    margin: "-50px 0px -50px 0px"
  });

  const variants = animationVariants[animation];

  const glassStyles = glassEffect ? 
    'backdrop-blur-sm bg-white/5 border border-white/10 shadow-lg rounded-2xl' : '';
  
  const hoverStyles = hoverEffect ? 
    'hover:shadow-xl hover:scale-[1.02] hover:bg-white/8 transition-all duration-300' : '';

  return (
    <motion.div
      ref={ref}
      initial="hidden"
      animate={isInView ? "visible" : "hidden"}
      variants={variants}
      transition={{
        duration,
        delay: delay + stagger,
        ease: [0.25, 0.25, 0, 1],
        type: "tween"
      }}
      className={`${glassStyles} ${hoverStyles} ${className}`}
      whileHover={hoverEffect ? { 
        scale: 1.02,
        transition: { duration: 0.2 }
      } : undefined}
    >
      {children}
    </motion.div>
  );
}

// Preset components for common use cases
export function FadeInReveal({ 
  children, 
  delay = 0, 
  className = '' 
}: { 
  children: React.ReactNode; 
  delay?: number; 
  className?: string; 
}) {
  return (
    <ScrollReveal animation="fadeIn" delay={delay} className={className}>
      {children}
    </ScrollReveal>
  );
}

export function SlideUpReveal({ 
  children, 
  delay = 0, 
  className = '' 
}: { 
  children: React.ReactNode; 
  delay?: number; 
  className?: string; 
}) {
  return (
    <ScrollReveal animation="slideUp" delay={delay} className={className}>
      {children}
    </ScrollReveal>
  );
}

export function GlassReveal({ 
  children, 
  delay = 0, 
  className = '',
  hoverEffect = true 
}: { 
  children: React.ReactNode; 
  delay?: number; 
  className?: string;
  hoverEffect?: boolean;
}) {
  return (
    <ScrollReveal 
      animation="slideUp" 
      delay={delay} 
      className={`p-4 sm:p-6 mx-2 sm:mx-4 ${className}`}
      glassEffect={true}
      hoverEffect={hoverEffect}
    >
      {children}
    </ScrollReveal>
  );
}

export function StaggeredReveal({ 
  children, 
  staggerDelay = 0.1,
  className = '' 
}: { 
  children: React.ReactNode[]; 
  staggerDelay?: number;
  className?: string;
}) {
  return (
    <div className={className}>
      {React.Children.map(children, (child, index) => (
        <ScrollReveal 
          key={index}
          animation="slideUp" 
          delay={index * staggerDelay}
          className="mb-4"
        >
          {child}
        </ScrollReveal>
      ))}
    </div>
  );
}
