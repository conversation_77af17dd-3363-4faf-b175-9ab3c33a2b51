"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./components/ui/AnimatedBackground.tsx":
/*!**********************************************!*\
  !*** ./components/ui/AnimatedBackground.tsx ***!
  \**********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ AnimatedBackground)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\nfunction AnimatedBackground(param) {\n    let { variant = 'hero', className = '', children } = param;\n    const getVariantConfig = ()=>{\n        switch(variant){\n            case 'hero':\n                return {\n                    gradients: [\n                        {\n                            className: \"absolute inset-0 bg-gradient-to-br from-purple-500/15 via-blue-400/10 to-teal-400/15\",\n                            animate: {\n                                opacity: [\n                                    0.3,\n                                    0.6,\n                                    0.3\n                                ]\n                            },\n                            transition: {\n                                duration: 8,\n                                repeat: Infinity,\n                                ease: \"easeInOut\"\n                            }\n                        },\n                        {\n                            className: \"absolute inset-0 bg-gradient-to-tl from-teal-300/10 via-transparent to-purple-500/8\",\n                            animate: {\n                                opacity: [\n                                    0.2,\n                                    0.5,\n                                    0.2\n                                ]\n                            },\n                            transition: {\n                                duration: 12,\n                                repeat: Infinity,\n                                ease: \"easeInOut\",\n                                delay: 2\n                            }\n                        }\n                    ],\n                    orbs: [\n                        {\n                            className: \"absolute top-[5%] right-[-5%] w-[600px] h-[600px] rounded-full bg-gradient-to-br from-purple-400/25 to-blue-400/15 blur-3xl\",\n                            animate: {\n                                scale: [\n                                    1,\n                                    1.2,\n                                    1\n                                ],\n                                opacity: [\n                                    0.3,\n                                    0.6,\n                                    0.3\n                                ],\n                                x: [\n                                    0,\n                                    50,\n                                    0\n                                ],\n                                y: [\n                                    0,\n                                    -30,\n                                    0\n                                ]\n                            },\n                            transition: {\n                                duration: 12,\n                                repeat: Infinity,\n                                ease: \"easeInOut\"\n                            }\n                        },\n                        {\n                            className: \"absolute top-[30%] left-[-8%] w-[700px] h-[700px] rounded-full bg-gradient-to-tr from-blue-500/25 to-cyan-500/20 blur-3xl\",\n                            animate: {\n                                scale: [\n                                    1,\n                                    0.8,\n                                    1\n                                ],\n                                opacity: [\n                                    0.3,\n                                    0.6,\n                                    0.3\n                                ],\n                                x: [\n                                    0,\n                                    -40,\n                                    0\n                                ],\n                                y: [\n                                    0,\n                                    40,\n                                    0\n                                ]\n                            },\n                            transition: {\n                                duration: 15,\n                                repeat: Infinity,\n                                ease: \"easeInOut\",\n                                delay: 2\n                            }\n                        },\n                        {\n                            className: \"absolute bottom-[0%] right-[10%] w-[500px] h-[500px] rounded-full bg-gradient-to-bl from-emerald-500/25 to-teal-500/20 blur-3xl\",\n                            animate: {\n                                scale: [\n                                    1,\n                                    1.3,\n                                    1\n                                ],\n                                opacity: [\n                                    0.35,\n                                    0.65,\n                                    0.35\n                                ],\n                                x: [\n                                    0,\n                                    -20,\n                                    0\n                                ],\n                                y: [\n                                    0,\n                                    20,\n                                    0\n                                ]\n                            },\n                            transition: {\n                                duration: 10,\n                                repeat: Infinity,\n                                ease: \"easeInOut\",\n                                delay: 4\n                            }\n                        },\n                        {\n                            className: \"absolute top-[60%] left-[20%] w-[400px] h-[400px] rounded-full bg-gradient-to-tr from-yellow-500/20 to-orange-500/15 blur-3xl\",\n                            animate: {\n                                scale: [\n                                    1,\n                                    1.1,\n                                    1\n                                ],\n                                opacity: [\n                                    0.25,\n                                    0.5,\n                                    0.25\n                                ],\n                                x: [\n                                    0,\n                                    -20,\n                                    0\n                                ],\n                                y: [\n                                    0,\n                                    15,\n                                    0\n                                ]\n                            },\n                            transition: {\n                                duration: 14,\n                                repeat: Infinity,\n                                ease: \"easeInOut\",\n                                delay: 6\n                            }\n                        }\n                    ],\n                    particles: [\n                        {\n                            className: \"absolute top-[15%] left-[15%] w-3 h-3 bg-theme-accent-primary/40 rounded-full\",\n                            animate: {\n                                y: [\n                                    0,\n                                    -20,\n                                    0\n                                ],\n                                opacity: [\n                                    0.4,\n                                    0.8,\n                                    0.4\n                                ],\n                                scale: [\n                                    1,\n                                    1.2,\n                                    1\n                                ]\n                            },\n                            transition: {\n                                duration: 6,\n                                repeat: Infinity,\n                                ease: \"easeInOut\"\n                            }\n                        },\n                        {\n                            className: \"absolute top-[45%] right-[25%] w-4 h-4 bg-theme-accent-secondary/50 rounded-full\",\n                            animate: {\n                                y: [\n                                    0,\n                                    -15,\n                                    0\n                                ],\n                                x: [\n                                    0,\n                                    10,\n                                    0\n                                ],\n                                opacity: [\n                                    0.5,\n                                    0.9,\n                                    0.5\n                                ]\n                            },\n                            transition: {\n                                duration: 8,\n                                repeat: Infinity,\n                                ease: \"easeInOut\",\n                                delay: 1\n                            }\n                        },\n                        {\n                            className: \"absolute top-[70%] left-[35%] w-2 h-2 bg-blue-400/60 rounded-full\",\n                            animate: {\n                                y: [\n                                    0,\n                                    -25,\n                                    0\n                                ],\n                                opacity: [\n                                    0.6,\n                                    1,\n                                    0.6\n                                ]\n                            },\n                            transition: {\n                                duration: 7,\n                                repeat: Infinity,\n                                ease: \"easeInOut\",\n                                delay: 2\n                            }\n                        },\n                        {\n                            className: \"absolute top-[25%] right-[55%] w-3.5 h-3.5 bg-purple-400/50 rounded-full\",\n                            animate: {\n                                y: [\n                                    0,\n                                    -18,\n                                    0\n                                ],\n                                x: [\n                                    0,\n                                    -8,\n                                    0\n                                ],\n                                opacity: [\n                                    0.5,\n                                    0.85,\n                                    0.5\n                                ]\n                            },\n                            transition: {\n                                duration: 9,\n                                repeat: Infinity,\n                                ease: \"easeInOut\",\n                                delay: 3\n                            }\n                        }\n                    ],\n                    geometric: [\n                        {\n                            className: \"absolute top-[10%] left-[5%] w-24 h-24 border border-theme-accent-primary/25\",\n                            animate: {\n                                rotate: [\n                                    0,\n                                    360\n                                ],\n                                scale: [\n                                    1,\n                                    1.1,\n                                    1\n                                ],\n                                opacity: [\n                                    0.25,\n                                    0.5,\n                                    0.25\n                                ]\n                            },\n                            transition: {\n                                duration: 20,\n                                repeat: Infinity,\n                                ease: \"linear\"\n                            }\n                        },\n                        {\n                            className: \"absolute bottom-[15%] right-[10%] w-20 h-20 border border-theme-accent-secondary/25\",\n                            animate: {\n                                rotate: [\n                                    360,\n                                    0\n                                ],\n                                scale: [\n                                    1,\n                                    0.9,\n                                    1\n                                ],\n                                opacity: [\n                                    0.25,\n                                    0.45,\n                                    0.25\n                                ]\n                            },\n                            transition: {\n                                duration: 25,\n                                repeat: Infinity,\n                                ease: \"linear\"\n                            }\n                        }\n                    ]\n                };\n            case 'section':\n                return {\n                    gradients: [\n                        {\n                            className: \"absolute inset-0 bg-gradient-to-r from-white/10 via-white/5 to-white/10\",\n                            animate: {\n                                opacity: [\n                                    0.1,\n                                    0.2,\n                                    0.1\n                                ]\n                            },\n                            transition: {\n                                duration: 6,\n                                repeat: Infinity,\n                                ease: \"easeInOut\"\n                            }\n                        }\n                    ],\n                    orbs: [\n                        {\n                            className: \"absolute top-[-20%] right-[-20%] w-[300px] h-[300px] rounded-full bg-gradient-to-br from-theme-accent-primary/15 to-theme-accent-secondary/10 blur-2xl\",\n                            animate: {\n                                scale: [\n                                    1,\n                                    1.1,\n                                    1\n                                ],\n                                opacity: [\n                                    0.15,\n                                    0.3,\n                                    0.15\n                                ]\n                            },\n                            transition: {\n                                duration: 8,\n                                repeat: Infinity,\n                                ease: \"easeInOut\"\n                            }\n                        },\n                        {\n                            className: \"absolute bottom-[-20%] left-[-20%] w-[250px] h-[250px] rounded-full bg-gradient-to-tr from-blue-400/10 to-purple-400/10 blur-2xl\",\n                            animate: {\n                                scale: [\n                                    1,\n                                    0.9,\n                                    1\n                                ],\n                                opacity: [\n                                    0.1,\n                                    0.25,\n                                    0.1\n                                ]\n                            },\n                            transition: {\n                                duration: 10,\n                                repeat: Infinity,\n                                ease: \"easeInOut\",\n                                delay: 2\n                            }\n                        }\n                    ],\n                    particles: [\n                        {\n                            className: \"absolute top-[20%] left-[20%] w-1.5 h-1.5 bg-theme-accent-primary/30 rounded-full\",\n                            animate: {\n                                y: [\n                                    0,\n                                    -10,\n                                    0\n                                ],\n                                opacity: [\n                                    0.3,\n                                    0.6,\n                                    0.3\n                                ]\n                            },\n                            transition: {\n                                duration: 5,\n                                repeat: Infinity,\n                                ease: \"easeInOut\"\n                            }\n                        },\n                        {\n                            className: \"absolute bottom-[30%] right-[30%] w-2 h-2 bg-theme-accent-secondary/40 rounded-full\",\n                            animate: {\n                                y: [\n                                    0,\n                                    -8,\n                                    0\n                                ],\n                                opacity: [\n                                    0.4,\n                                    0.7,\n                                    0.4\n                                ]\n                            },\n                            transition: {\n                                duration: 6,\n                                repeat: Infinity,\n                                ease: \"easeInOut\",\n                                delay: 1\n                            }\n                        }\n                    ],\n                    geometric: []\n                };\n            default:\n                return {\n                    gradients: [\n                        {\n                            className: \"absolute inset-0 bg-gradient-to-br from-gray-50/20 to-white/10\",\n                            animate: {\n                                opacity: [\n                                    0.1,\n                                    0.15,\n                                    0.1\n                                ]\n                            },\n                            transition: {\n                                duration: 4,\n                                repeat: Infinity,\n                                ease: \"easeInOut\"\n                            }\n                        }\n                    ],\n                    orbs: [\n                        {\n                            className: \"absolute top-[10%] right-[-10%] w-[200px] h-[200px] rounded-full bg-gradient-to-br from-theme-accent-primary/10 to-theme-accent-secondary/5 blur-xl\",\n                            animate: {\n                                scale: [\n                                    1,\n                                    1.05,\n                                    1\n                                ],\n                                opacity: [\n                                    0.1,\n                                    0.2,\n                                    0.1\n                                ]\n                            },\n                            transition: {\n                                duration: 6,\n                                repeat: Infinity,\n                                ease: \"easeInOut\"\n                            }\n                        }\n                    ],\n                    particles: [],\n                    geometric: []\n                };\n        }\n    };\n    const config = getVariantConfig();\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"relative overflow-hidden \".concat(className),\n        children: [\n            config.gradients.map((gradient, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div, {\n                    className: gradient.className,\n                    animate: gradient.animate,\n                    transition: gradient.transition\n                }, \"gradient-\".concat(index), false, {\n                    fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\ui\\\\AnimatedBackground.tsx\",\n                    lineNumber: 212,\n                    columnNumber: 9\n                }, this)),\n            config.orbs.map((orb, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div, {\n                    className: orb.className,\n                    animate: orb.animate,\n                    transition: orb.transition\n                }, \"orb-\".concat(index), false, {\n                    fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\ui\\\\AnimatedBackground.tsx\",\n                    lineNumber: 222,\n                    columnNumber: 9\n                }, this)),\n            config.particles.map((particle, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div, {\n                    className: particle.className,\n                    animate: particle.animate,\n                    transition: particle.transition\n                }, \"particle-\".concat(index), false, {\n                    fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\ui\\\\AnimatedBackground.tsx\",\n                    lineNumber: 232,\n                    columnNumber: 9\n                }, this)),\n            config.geometric.map((shape, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div, {\n                    className: shape.className,\n                    animate: shape.animate,\n                    transition: shape.transition\n                }, \"shape-\".concat(index), false, {\n                    fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\ui\\\\AnimatedBackground.tsx\",\n                    lineNumber: 242,\n                    columnNumber: 9\n                }, this)),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute inset-0 opacity-[0.02] bg-[radial-gradient(#000_1px,transparent_1px)] bg-[size:50px_50px]\"\n            }, void 0, false, {\n                fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\ui\\\\AnimatedBackground.tsx\",\n                lineNumber: 251,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative z-10\",\n                children: children\n            }, void 0, false, {\n                fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\ui\\\\AnimatedBackground.tsx\",\n                lineNumber: 254,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\ui\\\\AnimatedBackground.tsx\",\n        lineNumber: 209,\n        columnNumber: 5\n    }, this);\n}\n_c = AnimatedBackground;\nvar _c;\n$RefreshReg$(_c, \"AnimatedBackground\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/ui/AnimatedBackground.tsx\n"));

/***/ })

});