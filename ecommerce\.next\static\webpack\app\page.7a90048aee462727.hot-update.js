"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./app/page.tsx":
/*!**********************!*\
  !*** ./app/page.tsx ***!
  \**********************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_use_toast__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../components/ui/use-toast */ \"(app-pages-browser)/./components/ui/use-toast.ts\");\n/* harmony import */ var _constant_urls__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../constant/urls */ \"(app-pages-browser)/./constant/urls.ts\");\n/* harmony import */ var _hooks_useApi__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../hooks/useApi */ \"(app-pages-browser)/./hooks/useApi.ts\");\n/* harmony import */ var _layout_MainHOF__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../layout/MainHOF */ \"(app-pages-browser)/./layout/MainHOF.tsx\");\n/* harmony import */ var swiper_css_autoplay__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! swiper/css/autoplay */ \"(app-pages-browser)/./node_modules/swiper/modules/autoplay.css\");\n/* harmony import */ var _components_ui_TrustIndicators__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/TrustIndicators */ \"(app-pages-browser)/./components/ui/TrustIndicators.tsx\");\n/* harmony import */ var _components_ClientOnly__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/ClientOnly */ \"(app-pages-browser)/./components/ClientOnly.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n// Lazy load components outside of the component function\nconst HeroCarousel = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1___default().lazy(()=>__webpack_require__.e(/*! import() */ \"_app-pages-browser_components_home_hero-carousel_tsx\").then(__webpack_require__.bind(__webpack_require__, /*! @/components/home/<USER>/ \"(app-pages-browser)/./components/home/<USER>")));\n_c = HeroCarousel;\nconst ProductSection = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1___default().lazy(()=>__webpack_require__.e(/*! import() */ \"_app-pages-browser_components_home_ProductSection_tsx\").then(__webpack_require__.bind(__webpack_require__, /*! @/components/home/<USER>/ \"(app-pages-browser)/./components/home/<USER>")));\n_c1 = ProductSection;\nconst CategoryTabs = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1___default().lazy(()=>__webpack_require__.e(/*! import() */ \"_app-pages-browser_components_home_CategoryTabs_tsx\").then(__webpack_require__.bind(__webpack_require__, /*! @/components/home/<USER>/ \"(app-pages-browser)/./components/home/<USER>")));\n_c2 = CategoryTabs;\nconst ProductCategories = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1___default().lazy(()=>__webpack_require__.e(/*! import() */ \"_app-pages-browser_components_home_ProductCategories_tsx\").then(__webpack_require__.bind(__webpack_require__, /*! @/components/home/<USER>/ \"(app-pages-browser)/./components/home/<USER>")));\n_c3 = ProductCategories;\nconst Homepage = ()=>{\n    _s();\n    const { toast } = (0,_components_ui_use_toast__WEBPACK_IMPORTED_MODULE_2__.useToast)();\n    // Use a single API instance for all API calls\n    const { data, read } = (0,_hooks_useApi__WEBPACK_IMPORTED_MODULE_4__[\"default\"])(_constant_urls__WEBPACK_IMPORTED_MODULE_3__.MAIN_URL || '');\n    // Create state variables to store different data types\n    const [futureProduct, setFutureProduct] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [futureProductLoading, setFutureProductLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [popularProduct, setPopularProduct] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [popularProductLoading, setPopularProductLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [categories, setCategories] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [categoriesLoading, setCategoriesLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Use refs to track if data has been fetched\n    const initialDataFetchedRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(false);\n    const [categoryProducts, setCategoryProducts] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    // Track if category products have been fetched\n    const categoryProductsFetchedRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(false);\n    // Function to fetch products for each category - optimized to prevent continuous API calls\n    const fetchCategoryProducts = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"Homepage.useCallback[fetchCategoryProducts]\": async ()=>{\n            // Skip if categories aren't loaded yet\n            if (!Array.isArray(categories) || categories.length === 0) return;\n            // Skip if we've already fetched and have data\n            if (categoryProductsFetchedRef.current && categoryProducts.length > 0 && categoryProducts.every({\n                \"Homepage.useCallback[fetchCategoryProducts]\": (cat)=>!cat.loading\n            }[\"Homepage.useCallback[fetchCategoryProducts]\"])) {\n                return;\n            }\n            // Limit to first 6 categories to avoid overwhelming the page and reduce API calls\n            const limitedCategories = categories.slice(0, 6);\n            // Set initial loading state\n            if (categoryProducts.length === 0) {\n                const initialCategoryProducts = limitedCategories.map({\n                    \"Homepage.useCallback[fetchCategoryProducts].initialCategoryProducts\": (category)=>({\n                            category,\n                            products: [],\n                            loading: true\n                        })\n                }[\"Homepage.useCallback[fetchCategoryProducts].initialCategoryProducts\"]);\n                setCategoryProducts(initialCategoryProducts);\n            }\n            // Fetch products for each category\n            const promises = limitedCategories.map({\n                \"Homepage.useCallback[fetchCategoryProducts].promises\": async (category, index)=>{\n                    try {\n                        var _result_results;\n                        // Use the single read function from our consolidated API instance\n                        const result = await read(\"\".concat((0,_constant_urls__WEBPACK_IMPORTED_MODULE_3__.CATEGORIZE_PRODUCTS)(category.slug), \"?page_size=8\"));\n                        return {\n                            index,\n                            category,\n                            products: (result === null || result === void 0 ? void 0 : (_result_results = result.results) === null || _result_results === void 0 ? void 0 : _result_results.products) || [],\n                            success: true\n                        };\n                    } catch (error) {\n                        console.error(\"Error fetching products for \".concat(category.name, \":\"), error);\n                        return {\n                            index,\n                            category,\n                            products: [],\n                            success: false\n                        };\n                    }\n                }\n            }[\"Homepage.useCallback[fetchCategoryProducts].promises\"]);\n            // Wait for all promises to resolve\n            const results = await Promise.all(promises);\n            // Update state once with all results\n            setCategoryProducts({\n                \"Homepage.useCallback[fetchCategoryProducts]\": (prev)=>{\n                    // Start with previous state or empty array\n                    const newState = prev.length > 0 ? [\n                        ...prev\n                    ] : limitedCategories.map({\n                        \"Homepage.useCallback[fetchCategoryProducts]\": (cat)=>({\n                                category: cat,\n                                products: [],\n                                loading: true\n                            })\n                    }[\"Homepage.useCallback[fetchCategoryProducts]\"]);\n                    // Update with new results\n                    results.forEach({\n                        \"Homepage.useCallback[fetchCategoryProducts]\": (result)=>{\n                            if (newState[result.index]) {\n                                newState[result.index] = {\n                                    ...newState[result.index],\n                                    products: result.products,\n                                    loading: false\n                                };\n                            }\n                        }\n                    }[\"Homepage.useCallback[fetchCategoryProducts]\"]);\n                    // Mark as fetched\n                    categoryProductsFetchedRef.current = true;\n                    return newState;\n                }\n            }[\"Homepage.useCallback[fetchCategoryProducts]\"]);\n        }\n    }[\"Homepage.useCallback[fetchCategoryProducts]\"], [\n        categories,\n        read\n    ]);\n    // Load all initial data with a single useEffect to reduce renders and prevent continuous API calls\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Homepage.useEffect\": ()=>{\n            // Skip if we've already loaded the data\n            if (initialDataFetchedRef.current) return;\n            // Create a flag to track if the component is still mounted\n            let isMounted = true;\n            const loadInitialData = {\n                \"Homepage.useEffect.loadInitialData\": async ()=>{\n                    try {\n                        setCategoriesLoading(true);\n                        setFutureProductLoading(true);\n                        setPopularProductLoading(true);\n                        // Load categories first\n                        const categoriesResult = await read(_constant_urls__WEBPACK_IMPORTED_MODULE_3__.CATEGORIES);\n                        // Only continue if component is still mounted\n                        if (!isMounted) return;\n                        // Update categories state\n                        if (categoriesResult) {\n                            setCategories(categoriesResult);\n                        }\n                        setCategoriesLoading(false);\n                        // Load featured and popular products in parallel\n                        const [featuredResult, popularResult] = await Promise.all([\n                            read(_constant_urls__WEBPACK_IMPORTED_MODULE_3__.FUTURED_PRODUCTS),\n                            read(_constant_urls__WEBPACK_IMPORTED_MODULE_3__.PRODUCTS + '?page_size=10') // Fetch 10 products for the Discover section\n                        ]);\n                        if (!isMounted) return;\n                        // Update featured products state\n                        if (featuredResult) {\n                            setFutureProduct(featuredResult);\n                        }\n                        // Update popular products state\n                        if (popularResult) {\n                            setPopularProduct(popularResult);\n                        }\n                        // Mark as fetched to prevent duplicate API calls\n                        initialDataFetchedRef.current = true;\n                        setFutureProductLoading(false);\n                        setPopularProductLoading(false);\n                    } catch (error) {\n                        console.error(\"Error loading initial data:\", error);\n                        setCategoriesLoading(false);\n                        setFutureProductLoading(false);\n                        setPopularProductLoading(false);\n                    }\n                }\n            }[\"Homepage.useEffect.loadInitialData\"];\n            loadInitialData();\n            // Cleanup function to prevent state updates after unmount\n            return ({\n                \"Homepage.useEffect\": ()=>{\n                    isMounted = false;\n                }\n            })[\"Homepage.useEffect\"];\n        }\n    }[\"Homepage.useEffect\"], [\n        read\n    ]);\n    // Fetch products for each category when categories are loaded\n    // This useEffect now depends only on categories and fetchCategoryProducts\n    // It will only run when categories change, not on every render\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Homepage.useEffect\": ()=>{\n            if (categories && categories.length > 0 && !categoryProductsFetchedRef.current) {\n                fetchCategoryProducts();\n            }\n        }\n    }[\"Homepage.useEffect\"], [\n        fetchCategoryProducts,\n        categories\n    ]);\n    // We no longer need featuredProductsForHero since we're using HeroCarousel\n    // Get featured products for product section - memoized to prevent recalculations\n    const featuredProducts = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)({\n        \"Homepage.useMemo[featuredProducts]\": ()=>{\n            if (futureProduct && Array.isArray(futureProduct)) {\n                return futureProduct;\n            } else if (futureProduct && futureProduct.results && Array.isArray(futureProduct.results)) {\n                return futureProduct.results;\n            } else if (futureProduct && futureProduct.products && Array.isArray(futureProduct.products)) {\n                return futureProduct.products;\n            }\n            // Return empty array if no products are available - no more static fallback\n            return [];\n        }\n    }[\"Homepage.useMemo[featuredProducts]\"], [\n        futureProduct\n    ]);\n    // Get popular products - memoized to prevent recalculations\n    const popularProducts = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)({\n        \"Homepage.useMemo[popularProducts]\": ()=>{\n            // First check our dedicated popularProduct state\n            if (popularProduct && Array.isArray(popularProduct)) {\n                return popularProduct;\n            } else if (popularProduct && popularProduct.results && Array.isArray(popularProduct.results)) {\n                return popularProduct.results;\n            } else if (popularProduct && popularProduct.products && Array.isArray(popularProduct.products)) {\n                return popularProduct.products;\n            }\n            // Fallback to data from the main API call if popularProduct isn't available\n            if (data && Array.isArray(data)) {\n                return data;\n            } else if (data && data.results && Array.isArray(data.results)) {\n                return data.results;\n            } else if (data && data.products && Array.isArray(data.products)) {\n                return data.products;\n            }\n            // Return empty array if no products are available - no more static fallback\n            return [];\n        }\n    }[\"Homepage.useMemo[popularProducts]\"], [\n        popularProduct,\n        data\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"bg-theme-homepage w-full relative overflow-hidden\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute top-0 left-0 w-full h-full overflow-hidden pointer-events-none\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute top-0 left-0 w-full h-full bg-gradient-to-br from-blue-50/30 via-purple-50/20 to-emerald-50/30\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Triumph\\\\ecommerce\\\\app\\\\page.tsx\",\n                        lineNumber: 264,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute top-[10%] right-[-10%] w-[500px] h-[500px] rounded-full bg-gradient-to-br from-theme-accent-secondary/20 to-theme-accent-primary/10 blur-3xl animate-pulse-soft\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Triumph\\\\ecommerce\\\\app\\\\page.tsx\",\n                        lineNumber: 267,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute top-[40%] left-[-10%] w-[600px] h-[600px] rounded-full bg-gradient-to-tr from-theme-accent-primary/15 to-blue-400/10 blur-3xl animate-float\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Triumph\\\\ecommerce\\\\app\\\\page.tsx\",\n                        lineNumber: 268,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute bottom-[5%] right-[5%] w-[400px] h-[400px] rounded-full bg-gradient-to-bl from-purple-400/15 to-theme-accent-secondary/10 blur-3xl animate-pulse-soft\",\n                        style: {\n                            animationDelay: '1s'\n                        }\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Triumph\\\\ecommerce\\\\app\\\\page.tsx\",\n                        lineNumber: 269,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute top-[20%] left-[20%] w-2 h-2 bg-theme-accent-primary/30 rounded-full animate-float\",\n                        style: {\n                            animationDelay: '0.5s'\n                        }\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Triumph\\\\ecommerce\\\\app\\\\page.tsx\",\n                        lineNumber: 272,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute top-[60%] right-[30%] w-3 h-3 bg-theme-accent-secondary/40 rounded-full animate-float\",\n                        style: {\n                            animationDelay: '1.5s'\n                        }\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Triumph\\\\ecommerce\\\\app\\\\page.tsx\",\n                        lineNumber: 273,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute top-[80%] left-[40%] w-1.5 h-1.5 bg-blue-400/50 rounded-full animate-float\",\n                        style: {\n                            animationDelay: '2s'\n                        }\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Triumph\\\\ecommerce\\\\app\\\\page.tsx\",\n                        lineNumber: 274,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute top-[30%] right-[60%] w-2.5 h-2.5 bg-purple-400/40 rounded-full animate-float\",\n                        style: {\n                            animationDelay: '0.8s'\n                        }\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Triumph\\\\ecommerce\\\\app\\\\page.tsx\",\n                        lineNumber: 275,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute top-[15%] left-[10%] w-20 h-20 border border-theme-accent-primary/20 rotate-45 animate-spin\",\n                        style: {\n                            animationDuration: '20s'\n                        }\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Triumph\\\\ecommerce\\\\app\\\\page.tsx\",\n                        lineNumber: 278,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute bottom-[20%] right-[15%] w-16 h-16 border border-theme-accent-secondary/20 rotate-12 animate-spin\",\n                        style: {\n                            animationDuration: '25s',\n                            animationDirection: 'reverse'\n                        }\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Triumph\\\\ecommerce\\\\app\\\\page.tsx\",\n                        lineNumber: 279,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0 opacity-[0.02] bg-[radial-gradient(#000_1px,transparent_1px)] bg-[size:50px_50px]\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Triumph\\\\ecommerce\\\\app\\\\page.tsx\",\n                        lineNumber: 282,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\Triumph\\\\ecommerce\\\\app\\\\page.tsx\",\n                lineNumber: 262,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_layout_MainHOF__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"min-h-screen flex flex-col w-full relative z-10\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"relative z-20 animate-slide-in-mobile\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"backdrop-blur-sm bg-white/5 rounded-xl mx-2 sm:mx-4 mt-2 shadow-lg border border-white/10\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ProductCategories, {\n                                    categories: categories !== null && categories !== void 0 ? categories : [],\n                                    variant: \"navigation\",\n                                    showTitle: false,\n                                    showViewAll: false,\n                                    maxCategories: 12,\n                                    accentColor: \"primary\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Triumph\\\\ecommerce\\\\app\\\\page.tsx\",\n                                    lineNumber: 291,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Triumph\\\\ecommerce\\\\app\\\\page.tsx\",\n                                lineNumber: 290,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Triumph\\\\ecommerce\\\\app\\\\page.tsx\",\n                            lineNumber: 289,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ClientOnly__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((react__WEBPACK_IMPORTED_MODULE_1___default().Suspense), {\n                                fallback: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-full space-y-8 px-2 sm:px-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-full h-[400px] bg-gradient-to-r from-gray-100 to-gray-50 animate-shine rounded-xl shadow-lg\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Triumph\\\\ecommerce\\\\app\\\\page.tsx\",\n                                            lineNumber: 306,\n                                            columnNumber: 17\n                                        }, void 0),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-full h-[300px] bg-gradient-to-r from-gray-100 to-gray-50 animate-shine rounded-xl shadow-lg\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Triumph\\\\ecommerce\\\\app\\\\page.tsx\",\n                                            lineNumber: 307,\n                                            columnNumber: 17\n                                        }, void 0),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-full h-[300px] bg-gradient-to-r from-gray-100 to-gray-50 animate-shine rounded-xl shadow-lg\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Triumph\\\\ecommerce\\\\app\\\\page.tsx\",\n                                            lineNumber: 308,\n                                            columnNumber: 17\n                                        }, void 0)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Triumph\\\\ecommerce\\\\app\\\\page.tsx\",\n                                    lineNumber: 305,\n                                    columnNumber: 15\n                                }, void 0),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                                        className: \"relative w-full fade-in-professional\",\n                                        style: {\n                                            animationDelay: '0.2s'\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"transform transition-all duration-700 hover:scale-[1.01]\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(HeroCarousel, {}, void 0, false, {\n                                                    fileName: \"D:\\\\Triumph\\\\ecommerce\\\\app\\\\page.tsx\",\n                                                    lineNumber: 314,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Triumph\\\\ecommerce\\\\app\\\\page.tsx\",\n                                                lineNumber: 313,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"relative z-10 mt-6 mb-8 fade-in-professional\",\n                                                style: {\n                                                    animationDelay: '0.4s'\n                                                },\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_TrustIndicators__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {}, void 0, false, {\n                                                    fileName: \"D:\\\\Triumph\\\\ecommerce\\\\app\\\\page.tsx\",\n                                                    lineNumber: 317,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Triumph\\\\ecommerce\\\\app\\\\page.tsx\",\n                                                lineNumber: 316,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Triumph\\\\ecommerce\\\\app\\\\page.tsx\",\n                                        lineNumber: 312,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ProductSection, {\n                                        title: \"Featured Products\",\n                                        subtitle: \"Discover our handpicked selection of premium products\",\n                                        products: featuredProducts,\n                                        loading: futureProductLoading,\n                                        viewAllLink: \"/shop\",\n                                        accentColor: \"primary\",\n                                        columns: {\n                                            xs: 2,\n                                            sm: 2,\n                                            md: 3,\n                                            lg: 4,\n                                            xl: 4,\n                                            \"2xl\": 5\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Triumph\\\\ecommerce\\\\app\\\\page.tsx\",\n                                        lineNumber: 322,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ProductSection, {\n                                        title: \"Discover Products\",\n                                        subtitle: \"Explore our most popular items\",\n                                        products: popularProducts,\n                                        loading: popularProductLoading,\n                                        viewAllLink: \"/shop\",\n                                        accentColor: \"secondary\",\n                                        columns: {\n                                            xs: 2,\n                                            sm: 2,\n                                            md: 3,\n                                            lg: 4,\n                                            xl: 4,\n                                            \"2xl\": 5\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Triumph\\\\ecommerce\\\\app\\\\page.tsx\",\n                                        lineNumber: 340,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ProductCategories, {\n                                        title: \"Shop by Category\",\n                                        subtitle: \"Browse our collection by category\",\n                                        categories: categories || [],\n                                        accentColor: \"tertiary\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Triumph\\\\ecommerce\\\\app\\\\page.tsx\",\n                                        lineNumber: 358,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    categoryProducts.some((cat)=>cat.products && cat.products.length > 0) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CategoryTabs, {\n                                        title: \"Browse Products by Category\",\n                                        subtitle: \"Filter products by your favorite categories\",\n                                        categories: categories || [],\n                                        categoryProducts: categoryProducts.filter((cat)=>cat.products && cat.products.length > 0),\n                                        accentColor: \"primary\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Triumph\\\\ecommerce\\\\app\\\\page.tsx\",\n                                        lineNumber: 367,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    categoryProducts.map((categoryData, categoryIndex)=>// Only show categories with at least 4 products\n                                        Array.isArray(categoryData.products) && categoryData.products.length >= 4 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ProductSection, {\n                                            title: categoryData.category.name,\n                                            products: categoryData.products,\n                                            loading: categoryData.loading,\n                                            viewAllLink: \"/shop?category=\".concat(categoryData.category.slug),\n                                            accentColor: categoryIndex % 3 === 0 ? \"primary\" : categoryIndex % 3 === 1 ? \"secondary\" : \"tertiary\",\n                                            columns: {\n                                                xs: 2,\n                                                sm: 2,\n                                                md: 3,\n                                                lg: 4,\n                                                xl: 4,\n                                                \"2xl\": 5\n                                            }\n                                        }, categoryData.category.id, false, {\n                                            fileName: \"D:\\\\Triumph\\\\ecommerce\\\\app\\\\page.tsx\",\n                                            lineNumber: 380,\n                                            columnNumber: 17\n                                        }, undefined) : null)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Triumph\\\\ecommerce\\\\app\\\\page.tsx\",\n                                lineNumber: 304,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Triumph\\\\ecommerce\\\\app\\\\page.tsx\",\n                            lineNumber: 303,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Triumph\\\\ecommerce\\\\app\\\\page.tsx\",\n                    lineNumber: 287,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"D:\\\\Triumph\\\\ecommerce\\\\app\\\\page.tsx\",\n                lineNumber: 286,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\Triumph\\\\ecommerce\\\\app\\\\page.tsx\",\n        lineNumber: 260,\n        columnNumber: 5\n    }, undefined);\n};\n_s(Homepage, \"t1NL8oCH1x/iqJpW5zHQdihbFlM=\", false, function() {\n    return [\n        _components_ui_use_toast__WEBPACK_IMPORTED_MODULE_2__.useToast,\n        _hooks_useApi__WEBPACK_IMPORTED_MODULE_4__[\"default\"]\n    ];\n});\n_c4 = Homepage;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Homepage);\nvar _c, _c1, _c2, _c3, _c4;\n$RefreshReg$(_c, \"HeroCarousel\");\n$RefreshReg$(_c1, \"ProductSection\");\n$RefreshReg$(_c2, \"CategoryTabs\");\n$RefreshReg$(_c3, \"ProductCategories\");\n$RefreshReg$(_c4, \"Homepage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/page.tsx\n"));

/***/ })

});