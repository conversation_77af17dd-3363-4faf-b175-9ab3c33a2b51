"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("_app-pages-browser_components_home_hero-carousel_tsx",{

/***/ "(app-pages-browser)/./components/home/<USER>":
/*!*************************************************!*\
  !*** ./components/home/<USER>
  \*************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ AmazonStyleCarousel)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/image */ \"(app-pages-browser)/./node_modules/next/dist/api/image.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronLeft_ChevronRight_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronLeft,ChevronRight!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-left.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronLeft_ChevronRight_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronLeft,ChevronRight!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-right.js\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./lib/utils.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\nfunction AmazonStyleCarousel(param) {\n    let { slides, autoplayInterval = 5000, className } = param;\n    _s();\n    const [currentSlide, setCurrentSlide] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [isAutoPlaying, setIsAutoPlaying] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const autoPlayRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const containerRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const [touchStart, setTouchStart] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [touchEnd, setTouchEnd] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // Reset autoplay timer when slide changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AmazonStyleCarousel.useEffect\": ()=>{\n            if (isAutoPlaying) {\n                if (autoPlayRef.current) {\n                    clearInterval(autoPlayRef.current);\n                }\n                autoPlayRef.current = setInterval({\n                    \"AmazonStyleCarousel.useEffect\": ()=>{\n                        setCurrentSlide({\n                            \"AmazonStyleCarousel.useEffect\": (prev)=>(prev + 1) % slides.length\n                        }[\"AmazonStyleCarousel.useEffect\"]);\n                    }\n                }[\"AmazonStyleCarousel.useEffect\"], autoplayInterval);\n            }\n            return ({\n                \"AmazonStyleCarousel.useEffect\": ()=>{\n                    if (autoPlayRef.current) {\n                        clearInterval(autoPlayRef.current);\n                    }\n                }\n            })[\"AmazonStyleCarousel.useEffect\"];\n        }\n    }[\"AmazonStyleCarousel.useEffect\"], [\n        currentSlide,\n        isAutoPlaying,\n        autoplayInterval,\n        slides.length\n    ]);\n    // Pause autoplay on hover\n    const pauseAutoPlay = ()=>setIsAutoPlaying(false);\n    const resumeAutoPlay = ()=>setIsAutoPlaying(true);\n    const goToSlide = (index)=>{\n        setCurrentSlide(index);\n    };\n    const goToPrevSlide = ()=>{\n        setCurrentSlide((prev)=>prev === 0 ? slides.length - 1 : prev - 1);\n    };\n    const goToNextSlide = ()=>{\n        setCurrentSlide((prev)=>(prev + 1) % slides.length);\n    };\n    // Handle touch events for mobile swipe\n    const handleTouchStart = (e)=>{\n        setTouchStart(e.targetTouches[0].clientX);\n    };\n    const handleTouchMove = (e)=>{\n        setTouchEnd(e.targetTouches[0].clientX);\n    };\n    const handleTouchEnd = ()=>{\n        if (!touchStart || !touchEnd) return;\n        const distance = touchStart - touchEnd;\n        const isLeftSwipe = distance > 50;\n        const isRightSwipe = distance < -50;\n        if (isLeftSwipe) {\n            goToNextSlide();\n        } else if (isRightSwipe) {\n            goToPrevSlide();\n        }\n        setTouchStart(null);\n        setTouchEnd(null);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_5__.cn)(\"relative overflow-hidden rounded-xl shadow-lg mt-2 bg-gradient-to-r from-theme-header to-theme-header/90\", className),\n        onMouseEnter: pauseAutoPlay,\n        onMouseLeave: resumeAutoPlay,\n        ref: containerRef,\n        onTouchStart: handleTouchStart,\n        onTouchMove: handleTouchMove,\n        onTouchEnd: handleTouchEnd,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute inset-0 bg-opacity-10 z-0 overflow-hidden\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"absolute inset-0 opacity-10 bg-[radial-gradient(#ffffff33_1px,transparent_1px)] bg-[size:20px_20px]\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\home\\\\AmazonStyleCarousel.tsx\",\n                    lineNumber: 120,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\home\\\\AmazonStyleCarousel.tsx\",\n                lineNumber: 119,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex transition-transform duration-700 ease-out h-[280px] xs:h-[320px] sm:h-[380px] md:h-[420px] lg:h-[480px] xl:h-[520px]\",\n                style: {\n                    transform: \"translateX(-\".concat(currentSlide * 100, \"%)\")\n                },\n                children: slides.map((slide, index)=>{\n                    var _slide_brand, _slide_brand1, _slide_brand2, _slide_brand3, _slide_brand4;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"min-w-full relative group cursor-pointer\",\n                        onClick: ()=>{\n                            try {\n                                window.location.href = slide.link || \"/shop\";\n                            } catch (error) {\n                                console.error(\"Navigation error:\", error);\n                                window.location.href = \"/shop\";\n                            }\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute inset-0 bg-gradient-to-r from-[#8a6f4d]/20 to-[#d9c3a9]/10\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\home\\\\AmazonStyleCarousel.tsx\",\n                                lineNumber: 137,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute top-0 right-0 bottom-0 w-[45%] xs:w-[42%] sm:w-[45%] md:w-[50%] flex items-center justify-center p-2 xs:p-3 sm:p-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"relative w-full h-full max-w-[95%] max-h-[85%] xs:max-h-[90%]\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                        src: slide.image || '/home/<USER>',\n                                        alt: slide.title || 'Product Image',\n                                        fill: true,\n                                        sizes: \"(max-width: 640px) 50vw, (max-width: 768px) 45vw, (max-width: 1024px) 50vw, 50vw\",\n                                        className: \"object-contain object-center\",\n                                        priority: index === 0,\n                                        onError: (e)=>{\n                                            // Fallback to a placeholder if image fails to load\n                                            const imgElement = e.currentTarget;\n                                            if (!imgElement.src.includes('placeholder') && !imgElement.src.includes('image-1.png')) {\n                                                imgElement.src = '/home/<USER>';\n                                            }\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\home\\\\AmazonStyleCarousel.tsx\",\n                                        lineNumber: 142,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\home\\\\AmazonStyleCarousel.tsx\",\n                                    lineNumber: 141,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\home\\\\AmazonStyleCarousel.tsx\",\n                                lineNumber: 140,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute inset-0 bg-gradient-to-r from-black/50 via-black/30 to-transparent\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\home\\\\AmazonStyleCarousel.tsx\",\n                                lineNumber: 161,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute inset-0 bg-[url('/patterns/dot-pattern.svg')] bg-repeat opacity-10\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\home\\\\AmazonStyleCarousel.tsx\",\n                                lineNumber: 164,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute inset-0 flex items-center\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-white p-2 xs:p-3 sm:p-4 md:p-6 lg:p-8 xl:p-12 w-[55%] xs:w-[58%] sm:w-[55%] md:w-[50%] lg:w-[45%] xl:w-[40%]\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-start gap-1.5 xs:gap-2 sm:gap-3 mb-1 xs:mb-1.5 sm:mb-2 md:mb-3\",\n                                            children: [\n                                                slide.brand && typeof slide.brand !== 'string' && (((_slide_brand = slide.brand) === null || _slide_brand === void 0 ? void 0 : _slide_brand.image_url) || ((_slide_brand1 = slide.brand) === null || _slide_brand1 === void 0 ? void 0 : _slide_brand1.image)) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex-shrink-0 mt-0.5\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-6 h-6 xs:w-8 xs:h-8 sm:w-10 sm:h-10 md:w-12 md:h-12 lg:w-14 lg:h-14 overflow-hidden rounded-md border border-white/80 shadow-lg bg-white flex items-center justify-center p-0.5\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                            src: ((_slide_brand2 = slide.brand) === null || _slide_brand2 === void 0 ? void 0 : _slide_brand2.image_url) || \"\".concat(\"http://localhost:8000\" || 0).concat((_slide_brand3 = slide.brand) === null || _slide_brand3 === void 0 ? void 0 : _slide_brand3.image),\n                                                            alt: \"\".concat((_slide_brand4 = slide.brand) === null || _slide_brand4 === void 0 ? void 0 : _slide_brand4.name, \" logo\"),\n                                                            className: \"max-w-full max-h-full object-contain\",\n                                                            onError: (e)=>{\n                                                                // Hide the image on error\n                                                                const imgElement = e.currentTarget;\n                                                                imgElement.style.display = 'none';\n                                                            }\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\home\\\\AmazonStyleCarousel.tsx\",\n                                                            lineNumber: 176,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\home\\\\AmazonStyleCarousel.tsx\",\n                                                        lineNumber: 175,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\home\\\\AmazonStyleCarousel.tsx\",\n                                                    lineNumber: 174,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                    className: \"text-sm xs:text-base sm:text-lg md:text-xl lg:text-2xl xl:text-3xl 2xl:text-4xl font-bold   [text-shadow:_0_1px_3px_rgb(0_0_0_/_50%)] leading-tight flex-1 pr-1\",\n                                                    children: slide.title\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\home\\\\AmazonStyleCarousel.tsx\",\n                                                    lineNumber: 191,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\home\\\\AmazonStyleCarousel.tsx\",\n                                            lineNumber: 171,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xs xs:text-xs sm:text-sm md:text-base lg:text-lg mb-1.5 xs:mb-2 sm:mb-3 md:mb-4 text-white/90   [text-shadow:_0_1px_2px_rgb(0_0_0_/_30%)] line-clamp-2 leading-snug max-w-[95%] break-words\",\n                                            children: slide.subtitle\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\home\\\\AmazonStyleCarousel.tsx\",\n                                            lineNumber: 198,\n                                            columnNumber: 17\n                                        }, this),\n                                        slide.code && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"inline-block bg-white/20 backdrop-blur-sm px-2 xs:px-2.5 sm:px-3 py-0.5 xs:py-1 rounded-full   text-xs xs:text-xs sm:text-sm md:text-base font-medium mb-1.5 xs:mb-2 sm:mb-3   [text-shadow:_0_1px_1px_rgb(0_0_0_/_20%)] whitespace-nowrap\",\n                                            children: slide.code\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\home\\\\AmazonStyleCarousel.tsx\",\n                                            lineNumber: 205,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"mt-1.5 xs:mt-2 sm:mt-3\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_4___default()), {\n                                                href: slide.link || \"/shop\",\n                                                onClick: (e)=>{\n                                                    e.stopPropagation();\n                                                // Additional error handling can be added here if needed\n                                                },\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                    className: \"bg-[#2ECC71] hover:bg-[#27AE60] text-white font-medium   px-2.5 xs:px-3 sm:px-4 md:px-6 py-1 xs:py-1.5 sm:py-2 rounded-full   transition-all duration-300 hover:shadow-lg hover:scale-105   text-xs xs:text-xs sm:text-sm md:text-base whitespace-nowrap\",\n                                                    children: slide.cta || \"Shop Now\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\home\\\\AmazonStyleCarousel.tsx\",\n                                                    lineNumber: 218,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\home\\\\AmazonStyleCarousel.tsx\",\n                                                lineNumber: 214,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\home\\\\AmazonStyleCarousel.tsx\",\n                                            lineNumber: 213,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\home\\\\AmazonStyleCarousel.tsx\",\n                                    lineNumber: 168,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\home\\\\AmazonStyleCarousel.tsx\",\n                                lineNumber: 167,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute bottom-0 left-0 right-[40%] xs:right-[42%] sm:right-[45%] md:right-[50%] p-1.5 xs:p-2 sm:p-3 bg-gradient-to-t from-black/60 to-transparent\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-white text-xs xs:text-xs sm:text-sm\",\n                                    children: slide.specs && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"mb-0.5 [text-shadow:_0_1px_1px_rgb(0_0_0_/_30%)] leading-tight break-words max-w-full opacity-90\",\n                                        children: slide.specs\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\home\\\\AmazonStyleCarousel.tsx\",\n                                        lineNumber: 233,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\home\\\\AmazonStyleCarousel.tsx\",\n                                    lineNumber: 231,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\home\\\\AmazonStyleCarousel.tsx\",\n                                lineNumber: 230,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, index, true, {\n                        fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\home\\\\AmazonStyleCarousel.tsx\",\n                        lineNumber: 128,\n                        columnNumber: 11\n                    }, this);\n                })\n            }, void 0, false, {\n                fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\home\\\\AmazonStyleCarousel.tsx\",\n                lineNumber: 123,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                onClick: (e)=>{\n                    e.stopPropagation();\n                    goToPrevSlide();\n                },\n                className: \"absolute left-2 xs:left-4 top-1/2 -translate-y-1/2 w-8 h-8 xs:w-10 xs:h-10 items-center justify-center   bg-theme-accent-primary hover:bg-theme-accent-hover text-white rounded-full shadow-md z-10 transition-all duration-300   hover:shadow-lg hover:scale-110 hidden xs:inline-flex\",\n                \"aria-label\": \"Previous slide\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_ChevronRight_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                    className: \"h-4 w-4 xs:h-6 xs:w-6 text-white transition-transform duration-300 hover:-translate-x-0.5\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\home\\\\AmazonStyleCarousel.tsx\",\n                    lineNumber: 254,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\home\\\\AmazonStyleCarousel.tsx\",\n                lineNumber: 244,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                onClick: (e)=>{\n                    e.stopPropagation();\n                    goToNextSlide();\n                },\n                className: \"absolute right-2 xs:right-4 top-1/2 -translate-y-1/2 w-8 h-8 xs:w-10 xs:h-10 items-center justify-center   bg-theme-accent-primary hover:bg-theme-accent-hover text-white rounded-full shadow-md z-10 transition-all duration-300   hover:shadow-lg hover:scale-110 hidden xs:inline-flex\",\n                \"aria-label\": \"Next slide\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_ChevronRight_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                    className: \"h-4 w-4 xs:h-6 xs:w-6 text-white transition-transform duration-300 hover:translate-x-0.5\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\home\\\\AmazonStyleCarousel.tsx\",\n                    lineNumber: 267,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\home\\\\AmazonStyleCarousel.tsx\",\n                lineNumber: 257,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute bottom-2 xs:bottom-4 left-1/2 -translate-x-1/2 flex gap-1 xs:gap-2 z-10\",\n                children: slides.map((_, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: ()=>goToSlide(index),\n                        className: \"w-2 h-2 xs:w-3 xs:h-3 rounded-full transition-all duration-300 \".concat(index === currentSlide ? \"bg-theme-accent-secondary scale-110\" : \"bg-white/50 hover:bg-white/70 hover:scale-105\"),\n                        \"aria-label\": \"Go to slide \".concat(index + 1)\n                    }, index, false, {\n                        fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\home\\\\AmazonStyleCarousel.tsx\",\n                        lineNumber: 273,\n                        columnNumber: 11\n                    }, this))\n            }, void 0, false, {\n                fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\home\\\\AmazonStyleCarousel.tsx\",\n                lineNumber: 271,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\home\\\\AmazonStyleCarousel.tsx\",\n        lineNumber: 106,\n        columnNumber: 5\n    }, this);\n}\n_s(AmazonStyleCarousel, \"q+8ilV0we8mV9YlTA9aGx5Xg1EA=\");\n_c = AmazonStyleCarousel;\nvar _c;\n$RefreshReg$(_c, \"AmazonStyleCarousel\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/home/<USER>"));

/***/ })

});