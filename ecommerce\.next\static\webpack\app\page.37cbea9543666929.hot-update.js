"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./app/page.tsx":
/*!**********************!*\
  !*** ./app/page.tsx ***!
  \**********************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_use_toast__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../components/ui/use-toast */ \"(app-pages-browser)/./components/ui/use-toast.ts\");\n/* harmony import */ var _constant_urls__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../constant/urls */ \"(app-pages-browser)/./constant/urls.ts\");\n/* harmony import */ var _hooks_useApi__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../hooks/useApi */ \"(app-pages-browser)/./hooks/useApi.ts\");\n/* harmony import */ var _layout_MainHOF__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../layout/MainHOF */ \"(app-pages-browser)/./layout/MainHOF.tsx\");\n/* harmony import */ var swiper_css_autoplay__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! swiper/css/autoplay */ \"(app-pages-browser)/./node_modules/swiper/modules/autoplay.css\");\n/* harmony import */ var _components_ui_TrustIndicators__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/TrustIndicators */ \"(app-pages-browser)/./components/ui/TrustIndicators.tsx\");\n/* harmony import */ var _components_ClientOnly__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/ClientOnly */ \"(app-pages-browser)/./components/ClientOnly.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n// Lazy load components outside of the component function\nconst HeroCarousel = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1___default().lazy(()=>__webpack_require__.e(/*! import() */ \"_app-pages-browser_components_home_hero-carousel_tsx\").then(__webpack_require__.bind(__webpack_require__, /*! @/components/home/<USER>/ \"(app-pages-browser)/./components/home/<USER>")));\n_c = HeroCarousel;\nconst ProductSection = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1___default().lazy(()=>__webpack_require__.e(/*! import() */ \"_app-pages-browser_components_home_ProductSection_tsx\").then(__webpack_require__.bind(__webpack_require__, /*! @/components/home/<USER>/ \"(app-pages-browser)/./components/home/<USER>")));\n_c1 = ProductSection;\nconst CategoryTabs = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1___default().lazy(()=>__webpack_require__.e(/*! import() */ \"_app-pages-browser_components_home_CategoryTabs_tsx\").then(__webpack_require__.bind(__webpack_require__, /*! @/components/home/<USER>/ \"(app-pages-browser)/./components/home/<USER>")));\n_c2 = CategoryTabs;\nconst ProductCategories = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1___default().lazy(()=>__webpack_require__.e(/*! import() */ \"_app-pages-browser_components_home_ProductCategories_tsx\").then(__webpack_require__.bind(__webpack_require__, /*! @/components/home/<USER>/ \"(app-pages-browser)/./components/home/<USER>")));\n_c3 = ProductCategories;\nconst Homepage = ()=>{\n    _s();\n    const { toast } = (0,_components_ui_use_toast__WEBPACK_IMPORTED_MODULE_2__.useToast)();\n    // Use a single API instance for all API calls\n    const { data, read } = (0,_hooks_useApi__WEBPACK_IMPORTED_MODULE_4__[\"default\"])(_constant_urls__WEBPACK_IMPORTED_MODULE_3__.MAIN_URL || '');\n    // Create state variables to store different data types\n    const [futureProduct, setFutureProduct] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [futureProductLoading, setFutureProductLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [popularProduct, setPopularProduct] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [popularProductLoading, setPopularProductLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [categories, setCategories] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [categoriesLoading, setCategoriesLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Use refs to track if data has been fetched\n    const initialDataFetchedRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(false);\n    const [categoryProducts, setCategoryProducts] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    // Track if category products have been fetched\n    const categoryProductsFetchedRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(false);\n    // Function to fetch products for each category - optimized to prevent continuous API calls\n    const fetchCategoryProducts = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"Homepage.useCallback[fetchCategoryProducts]\": async ()=>{\n            // Skip if categories aren't loaded yet\n            if (!Array.isArray(categories) || categories.length === 0) return;\n            // Skip if we've already fetched and have data\n            if (categoryProductsFetchedRef.current && categoryProducts.length > 0 && categoryProducts.every({\n                \"Homepage.useCallback[fetchCategoryProducts]\": (cat)=>!cat.loading\n            }[\"Homepage.useCallback[fetchCategoryProducts]\"])) {\n                return;\n            }\n            // Limit to first 6 categories to avoid overwhelming the page and reduce API calls\n            const limitedCategories = categories.slice(0, 6);\n            // Set initial loading state\n            if (categoryProducts.length === 0) {\n                const initialCategoryProducts = limitedCategories.map({\n                    \"Homepage.useCallback[fetchCategoryProducts].initialCategoryProducts\": (category)=>({\n                            category,\n                            products: [],\n                            loading: true\n                        })\n                }[\"Homepage.useCallback[fetchCategoryProducts].initialCategoryProducts\"]);\n                setCategoryProducts(initialCategoryProducts);\n            }\n            // Fetch products for each category\n            const promises = limitedCategories.map({\n                \"Homepage.useCallback[fetchCategoryProducts].promises\": async (category, index)=>{\n                    try {\n                        var _result_results;\n                        // Use the single read function from our consolidated API instance\n                        const result = await read(\"\".concat((0,_constant_urls__WEBPACK_IMPORTED_MODULE_3__.CATEGORIZE_PRODUCTS)(category.slug), \"?page_size=8\"));\n                        return {\n                            index,\n                            category,\n                            products: (result === null || result === void 0 ? void 0 : (_result_results = result.results) === null || _result_results === void 0 ? void 0 : _result_results.products) || [],\n                            success: true\n                        };\n                    } catch (error) {\n                        console.error(\"Error fetching products for \".concat(category.name, \":\"), error);\n                        return {\n                            index,\n                            category,\n                            products: [],\n                            success: false\n                        };\n                    }\n                }\n            }[\"Homepage.useCallback[fetchCategoryProducts].promises\"]);\n            // Wait for all promises to resolve\n            const results = await Promise.all(promises);\n            // Update state once with all results\n            setCategoryProducts({\n                \"Homepage.useCallback[fetchCategoryProducts]\": (prev)=>{\n                    // Start with previous state or empty array\n                    const newState = prev.length > 0 ? [\n                        ...prev\n                    ] : limitedCategories.map({\n                        \"Homepage.useCallback[fetchCategoryProducts]\": (cat)=>({\n                                category: cat,\n                                products: [],\n                                loading: true\n                            })\n                    }[\"Homepage.useCallback[fetchCategoryProducts]\"]);\n                    // Update with new results\n                    results.forEach({\n                        \"Homepage.useCallback[fetchCategoryProducts]\": (result)=>{\n                            if (newState[result.index]) {\n                                newState[result.index] = {\n                                    ...newState[result.index],\n                                    products: result.products,\n                                    loading: false\n                                };\n                            }\n                        }\n                    }[\"Homepage.useCallback[fetchCategoryProducts]\"]);\n                    // Mark as fetched\n                    categoryProductsFetchedRef.current = true;\n                    return newState;\n                }\n            }[\"Homepage.useCallback[fetchCategoryProducts]\"]);\n        }\n    }[\"Homepage.useCallback[fetchCategoryProducts]\"], [\n        categories,\n        read\n    ]);\n    // Load all initial data with a single useEffect to reduce renders and prevent continuous API calls\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Homepage.useEffect\": ()=>{\n            // Skip if we've already loaded the data\n            if (initialDataFetchedRef.current) return;\n            // Create a flag to track if the component is still mounted\n            let isMounted = true;\n            const loadInitialData = {\n                \"Homepage.useEffect.loadInitialData\": async ()=>{\n                    try {\n                        setCategoriesLoading(true);\n                        setFutureProductLoading(true);\n                        setPopularProductLoading(true);\n                        // Load categories first\n                        const categoriesResult = await read(_constant_urls__WEBPACK_IMPORTED_MODULE_3__.CATEGORIES);\n                        // Only continue if component is still mounted\n                        if (!isMounted) return;\n                        // Update categories state\n                        if (categoriesResult) {\n                            setCategories(categoriesResult);\n                        }\n                        setCategoriesLoading(false);\n                        // Load featured and popular products in parallel\n                        const [featuredResult, popularResult] = await Promise.all([\n                            read(_constant_urls__WEBPACK_IMPORTED_MODULE_3__.FUTURED_PRODUCTS),\n                            read(_constant_urls__WEBPACK_IMPORTED_MODULE_3__.PRODUCTS + '?page_size=10') // Fetch 10 products for the Discover section\n                        ]);\n                        if (!isMounted) return;\n                        // Update featured products state\n                        if (featuredResult) {\n                            setFutureProduct(featuredResult);\n                        }\n                        // Update popular products state\n                        if (popularResult) {\n                            setPopularProduct(popularResult);\n                        }\n                        // Mark as fetched to prevent duplicate API calls\n                        initialDataFetchedRef.current = true;\n                        setFutureProductLoading(false);\n                        setPopularProductLoading(false);\n                    } catch (error) {\n                        console.error(\"Error loading initial data:\", error);\n                        setCategoriesLoading(false);\n                        setFutureProductLoading(false);\n                        setPopularProductLoading(false);\n                    }\n                }\n            }[\"Homepage.useEffect.loadInitialData\"];\n            loadInitialData();\n            // Cleanup function to prevent state updates after unmount\n            return ({\n                \"Homepage.useEffect\": ()=>{\n                    isMounted = false;\n                }\n            })[\"Homepage.useEffect\"];\n        }\n    }[\"Homepage.useEffect\"], [\n        read\n    ]);\n    // Fetch products for each category when categories are loaded\n    // This useEffect now depends only on categories and fetchCategoryProducts\n    // It will only run when categories change, not on every render\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Homepage.useEffect\": ()=>{\n            if (categories && categories.length > 0 && !categoryProductsFetchedRef.current) {\n                fetchCategoryProducts();\n            }\n        }\n    }[\"Homepage.useEffect\"], [\n        fetchCategoryProducts,\n        categories\n    ]);\n    // We no longer need featuredProductsForHero since we're using HeroCarousel\n    // Get featured products for product section - memoized to prevent recalculations\n    const featuredProducts = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)({\n        \"Homepage.useMemo[featuredProducts]\": ()=>{\n            if (futureProduct && Array.isArray(futureProduct)) {\n                return futureProduct;\n            } else if (futureProduct && futureProduct.results && Array.isArray(futureProduct.results)) {\n                return futureProduct.results;\n            } else if (futureProduct && futureProduct.products && Array.isArray(futureProduct.products)) {\n                return futureProduct.products;\n            }\n            // Return empty array if no products are available - no more static fallback\n            return [];\n        }\n    }[\"Homepage.useMemo[featuredProducts]\"], [\n        futureProduct\n    ]);\n    // Get popular products - memoized to prevent recalculations\n    const popularProducts = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)({\n        \"Homepage.useMemo[popularProducts]\": ()=>{\n            // First check our dedicated popularProduct state\n            if (popularProduct && Array.isArray(popularProduct)) {\n                return popularProduct;\n            } else if (popularProduct && popularProduct.results && Array.isArray(popularProduct.results)) {\n                return popularProduct.results;\n            } else if (popularProduct && popularProduct.products && Array.isArray(popularProduct.products)) {\n                return popularProduct.products;\n            }\n            // Fallback to data from the main API call if popularProduct isn't available\n            if (data && Array.isArray(data)) {\n                return data;\n            } else if (data && data.results && Array.isArray(data.results)) {\n                return data.results;\n            } else if (data && data.products && Array.isArray(data.products)) {\n                return data.products;\n            }\n            // Return empty array if no products are available - no more static fallback\n            return [];\n        }\n    }[\"Homepage.useMemo[popularProducts]\"], [\n        popularProduct,\n        data\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"bg-theme-homepage w-full relative overflow-hidden\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute top-0 left-0 w-full h-full overflow-hidden pointer-events-none\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute top-0 left-0 w-full h-full bg-gradient-to-br from-blue-50/30 via-purple-50/20 to-emerald-50/30\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Triumph\\\\ecommerce\\\\app\\\\page.tsx\",\n                        lineNumber: 264,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute top-[10%] right-[-10%] w-[500px] h-[500px] rounded-full bg-gradient-to-br from-theme-accent-secondary/20 to-theme-accent-primary/10 blur-3xl animate-pulse-soft\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Triumph\\\\ecommerce\\\\app\\\\page.tsx\",\n                        lineNumber: 267,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute top-[40%] left-[-10%] w-[600px] h-[600px] rounded-full bg-gradient-to-tr from-theme-accent-primary/15 to-blue-400/10 blur-3xl animate-float\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Triumph\\\\ecommerce\\\\app\\\\page.tsx\",\n                        lineNumber: 268,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute bottom-[5%] right-[5%] w-[400px] h-[400px] rounded-full bg-gradient-to-bl from-purple-400/15 to-theme-accent-secondary/10 blur-3xl animate-pulse-soft\",\n                        style: {\n                            animationDelay: '1s'\n                        }\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Triumph\\\\ecommerce\\\\app\\\\page.tsx\",\n                        lineNumber: 269,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute top-[20%] left-[20%] w-2 h-2 bg-theme-accent-primary/30 rounded-full animate-float\",\n                        style: {\n                            animationDelay: '0.5s'\n                        }\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Triumph\\\\ecommerce\\\\app\\\\page.tsx\",\n                        lineNumber: 272,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute top-[60%] right-[30%] w-3 h-3 bg-theme-accent-secondary/40 rounded-full animate-float\",\n                        style: {\n                            animationDelay: '1.5s'\n                        }\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Triumph\\\\ecommerce\\\\app\\\\page.tsx\",\n                        lineNumber: 273,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute top-[80%] left-[40%] w-1.5 h-1.5 bg-blue-400/50 rounded-full animate-float\",\n                        style: {\n                            animationDelay: '2s'\n                        }\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Triumph\\\\ecommerce\\\\app\\\\page.tsx\",\n                        lineNumber: 274,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute top-[30%] right-[60%] w-2.5 h-2.5 bg-purple-400/40 rounded-full animate-float\",\n                        style: {\n                            animationDelay: '0.8s'\n                        }\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Triumph\\\\ecommerce\\\\app\\\\page.tsx\",\n                        lineNumber: 275,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute top-[15%] left-[10%] w-20 h-20 border border-theme-accent-primary/20 rotate-45 animate-spin\",\n                        style: {\n                            animationDuration: '20s'\n                        }\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Triumph\\\\ecommerce\\\\app\\\\page.tsx\",\n                        lineNumber: 278,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute bottom-[20%] right-[15%] w-16 h-16 border border-theme-accent-secondary/20 rotate-12 animate-spin\",\n                        style: {\n                            animationDuration: '25s',\n                            animationDirection: 'reverse'\n                        }\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Triumph\\\\ecommerce\\\\app\\\\page.tsx\",\n                        lineNumber: 279,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0 opacity-[0.02] bg-[radial-gradient(#000_1px,transparent_1px)] bg-[size:50px_50px]\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Triumph\\\\ecommerce\\\\app\\\\page.tsx\",\n                        lineNumber: 282,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\Triumph\\\\ecommerce\\\\app\\\\page.tsx\",\n                lineNumber: 262,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_layout_MainHOF__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"min-h-screen flex flex-col w-full relative z-10\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"relative z-20 animate-slide-in-mobile\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"backdrop-blur-sm bg-white/5 rounded-xl mx-2 sm:mx-4 mt-2 shadow-lg border border-white/10\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ProductCategories, {\n                                    categories: categories !== null && categories !== void 0 ? categories : [],\n                                    variant: \"navigation\",\n                                    showTitle: false,\n                                    showViewAll: false,\n                                    maxCategories: 12,\n                                    accentColor: \"primary\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Triumph\\\\ecommerce\\\\app\\\\page.tsx\",\n                                    lineNumber: 291,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Triumph\\\\ecommerce\\\\app\\\\page.tsx\",\n                                lineNumber: 290,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Triumph\\\\ecommerce\\\\app\\\\page.tsx\",\n                            lineNumber: 289,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ClientOnly__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((react__WEBPACK_IMPORTED_MODULE_1___default().Suspense), {\n                                fallback: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-full space-y-8 px-2 sm:px-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-full h-[400px] bg-gradient-to-r from-gray-100 to-gray-50 animate-shine rounded-xl shadow-lg\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Triumph\\\\ecommerce\\\\app\\\\page.tsx\",\n                                            lineNumber: 306,\n                                            columnNumber: 17\n                                        }, void 0),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-full h-[300px] bg-gradient-to-r from-gray-100 to-gray-50 animate-shine rounded-xl shadow-lg\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Triumph\\\\ecommerce\\\\app\\\\page.tsx\",\n                                            lineNumber: 307,\n                                            columnNumber: 17\n                                        }, void 0),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-full h-[300px] bg-gradient-to-r from-gray-100 to-gray-50 animate-shine rounded-xl shadow-lg\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Triumph\\\\ecommerce\\\\app\\\\page.tsx\",\n                                            lineNumber: 308,\n                                            columnNumber: 17\n                                        }, void 0)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Triumph\\\\ecommerce\\\\app\\\\page.tsx\",\n                                    lineNumber: 305,\n                                    columnNumber: 15\n                                }, void 0),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                                        className: \"relative w-full fade-in-professional\",\n                                        style: {\n                                            animationDelay: '0.2s'\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"transform transition-all duration-700 hover:scale-[1.01]\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(HeroCarousel, {}, void 0, false, {\n                                                    fileName: \"D:\\\\Triumph\\\\ecommerce\\\\app\\\\page.tsx\",\n                                                    lineNumber: 314,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Triumph\\\\ecommerce\\\\app\\\\page.tsx\",\n                                                lineNumber: 313,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"relative z-10 mt-6 mb-8 fade-in-professional\",\n                                                style: {\n                                                    animationDelay: '0.4s'\n                                                },\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_TrustIndicators__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {}, void 0, false, {\n                                                    fileName: \"D:\\\\Triumph\\\\ecommerce\\\\app\\\\page.tsx\",\n                                                    lineNumber: 317,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Triumph\\\\ecommerce\\\\app\\\\page.tsx\",\n                                                lineNumber: 316,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Triumph\\\\ecommerce\\\\app\\\\page.tsx\",\n                                        lineNumber: 312,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"fade-in-professional\",\n                                        style: {\n                                            animationDelay: '0.6s'\n                                        },\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"backdrop-blur-sm bg-white/5 rounded-2xl mx-2 sm:mx-4 p-4 sm:p-6 shadow-lg border border-white/10 hover:shadow-xl transition-all duration-500\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ProductSection, {\n                                                title: \"Featured Products\",\n                                                subtitle: \"Discover our handpicked selection of premium products\",\n                                                products: featuredProducts,\n                                                loading: futureProductLoading,\n                                                viewAllLink: \"/shop\",\n                                                accentColor: \"primary\",\n                                                columns: {\n                                                    xs: 2,\n                                                    sm: 2,\n                                                    md: 3,\n                                                    lg: 4,\n                                                    xl: 4,\n                                                    \"2xl\": 5\n                                                }\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Triumph\\\\ecommerce\\\\app\\\\page.tsx\",\n                                                lineNumber: 324,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Triumph\\\\ecommerce\\\\app\\\\page.tsx\",\n                                            lineNumber: 323,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Triumph\\\\ecommerce\\\\app\\\\page.tsx\",\n                                        lineNumber: 322,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"fade-in-professional\",\n                                        style: {\n                                            animationDelay: '0.8s'\n                                        },\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"backdrop-blur-sm bg-white/5 rounded-2xl mx-2 sm:mx-4 p-4 sm:p-6 shadow-lg border border-white/10 hover:shadow-xl transition-all duration-500\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ProductSection, {\n                                                title: \"Discover Products\",\n                                                subtitle: \"Explore our most popular items\",\n                                                products: popularProducts,\n                                                loading: popularProductLoading,\n                                                viewAllLink: \"/shop\",\n                                                accentColor: \"secondary\",\n                                                columns: {\n                                                    xs: 2,\n                                                    sm: 2,\n                                                    md: 3,\n                                                    lg: 4,\n                                                    xl: 4,\n                                                    \"2xl\": 5\n                                                }\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Triumph\\\\ecommerce\\\\app\\\\page.tsx\",\n                                                lineNumber: 346,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Triumph\\\\ecommerce\\\\app\\\\page.tsx\",\n                                            lineNumber: 345,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Triumph\\\\ecommerce\\\\app\\\\page.tsx\",\n                                        lineNumber: 344,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ProductCategories, {\n                                        title: \"Shop by Category\",\n                                        subtitle: \"Browse our collection by category\",\n                                        categories: categories || [],\n                                        accentColor: \"tertiary\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Triumph\\\\ecommerce\\\\app\\\\page.tsx\",\n                                        lineNumber: 366,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    categoryProducts.some((cat)=>cat.products && cat.products.length > 0) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CategoryTabs, {\n                                        title: \"Browse Products by Category\",\n                                        subtitle: \"Filter products by your favorite categories\",\n                                        categories: categories || [],\n                                        categoryProducts: categoryProducts.filter((cat)=>cat.products && cat.products.length > 0),\n                                        accentColor: \"primary\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Triumph\\\\ecommerce\\\\app\\\\page.tsx\",\n                                        lineNumber: 375,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    categoryProducts.map((categoryData, categoryIndex)=>// Only show categories with at least 4 products\n                                        Array.isArray(categoryData.products) && categoryData.products.length >= 4 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ProductSection, {\n                                            title: categoryData.category.name,\n                                            products: categoryData.products,\n                                            loading: categoryData.loading,\n                                            viewAllLink: \"/shop?category=\".concat(categoryData.category.slug),\n                                            accentColor: categoryIndex % 3 === 0 ? \"primary\" : categoryIndex % 3 === 1 ? \"secondary\" : \"tertiary\",\n                                            columns: {\n                                                xs: 2,\n                                                sm: 2,\n                                                md: 3,\n                                                lg: 4,\n                                                xl: 4,\n                                                \"2xl\": 5\n                                            }\n                                        }, categoryData.category.id, false, {\n                                            fileName: \"D:\\\\Triumph\\\\ecommerce\\\\app\\\\page.tsx\",\n                                            lineNumber: 388,\n                                            columnNumber: 17\n                                        }, undefined) : null)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Triumph\\\\ecommerce\\\\app\\\\page.tsx\",\n                                lineNumber: 304,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Triumph\\\\ecommerce\\\\app\\\\page.tsx\",\n                            lineNumber: 303,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Triumph\\\\ecommerce\\\\app\\\\page.tsx\",\n                    lineNumber: 287,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"D:\\\\Triumph\\\\ecommerce\\\\app\\\\page.tsx\",\n                lineNumber: 286,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\Triumph\\\\ecommerce\\\\app\\\\page.tsx\",\n        lineNumber: 260,\n        columnNumber: 5\n    }, undefined);\n};\n_s(Homepage, \"t1NL8oCH1x/iqJpW5zHQdihbFlM=\", false, function() {\n    return [\n        _components_ui_use_toast__WEBPACK_IMPORTED_MODULE_2__.useToast,\n        _hooks_useApi__WEBPACK_IMPORTED_MODULE_4__[\"default\"]\n    ];\n});\n_c4 = Homepage;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Homepage);\nvar _c, _c1, _c2, _c3, _c4;\n$RefreshReg$(_c, \"HeroCarousel\");\n$RefreshReg$(_c1, \"ProductSection\");\n$RefreshReg$(_c2, \"CategoryTabs\");\n$RefreshReg$(_c3, \"ProductCategories\");\n$RefreshReg$(_c4, \"Homepage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/page.tsx\n"));

/***/ })

});