  backend/management/commands/clear_security_blocks.py
  backend/management/commands/security_audit.py
  backend/management/commands/security_cleanup.py
  backend/management/commands/test_security_notifications.py


  

****
donot use shree <PERSON><PERSON><PERSON> it is not enable currently
it is compulsory to use wrapper api if you api docs see there is diffrent enpoints to create order
****



<!-- **** -->
 <!-- prod db bosch product  -->
python simple_bosch_loader.py ../bosch_products_output.json
 python fix_sequences.py
<!-- **** -->


<!-- if slug not generated than run this -->
python manage.py shell -c "from products.models import Product; from django.utils.text import slugify; [setattr(p, 'slug', (lambda name: (lambda s, i=1: s if not Product.objects.filter(slug=s).exclude(pk=p.pk).exists() else next((f'{s}-{n}' for n in range(1, 9999) if not Product.objects.filter(slug=f'{s}-{n}').exclude(pk=p.pk).exists())))(slugify(name)))(p.name)) or p.save() for p in Product.objects.filter(slug__isnull=True)]"