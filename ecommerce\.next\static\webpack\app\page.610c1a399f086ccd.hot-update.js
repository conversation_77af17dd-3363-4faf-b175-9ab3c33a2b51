"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./app/page.tsx":
/*!**********************!*\
  !*** ./app/page.tsx ***!
  \**********************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_use_toast__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../components/ui/use-toast */ \"(app-pages-browser)/./components/ui/use-toast.ts\");\n/* harmony import */ var _constant_urls__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../constant/urls */ \"(app-pages-browser)/./constant/urls.ts\");\n/* harmony import */ var _hooks_useApi__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../hooks/useApi */ \"(app-pages-browser)/./hooks/useApi.ts\");\n/* harmony import */ var _layout_MainHOF__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../layout/MainHOF */ \"(app-pages-browser)/./layout/MainHOF.tsx\");\n/* harmony import */ var swiper_css_autoplay__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! swiper/css/autoplay */ \"(app-pages-browser)/./node_modules/swiper/modules/autoplay.css\");\n/* harmony import */ var _components_ui_TrustIndicators__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/TrustIndicators */ \"(app-pages-browser)/./components/ui/TrustIndicators.tsx\");\n/* harmony import */ var _components_ClientOnly__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/ClientOnly */ \"(app-pages-browser)/./components/ClientOnly.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n// Lazy load components outside of the component function\nconst HeroCarousel = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1___default().lazy(()=>__webpack_require__.e(/*! import() */ \"_app-pages-browser_components_home_hero-carousel_tsx\").then(__webpack_require__.bind(__webpack_require__, /*! @/components/home/<USER>/ \"(app-pages-browser)/./components/home/<USER>")));\n_c = HeroCarousel;\nconst ProductSection = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1___default().lazy(()=>__webpack_require__.e(/*! import() */ \"_app-pages-browser_components_home_ProductSection_tsx\").then(__webpack_require__.bind(__webpack_require__, /*! @/components/home/<USER>/ \"(app-pages-browser)/./components/home/<USER>")));\n_c1 = ProductSection;\nconst CategoryTabs = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1___default().lazy(()=>__webpack_require__.e(/*! import() */ \"_app-pages-browser_components_home_CategoryTabs_tsx\").then(__webpack_require__.bind(__webpack_require__, /*! @/components/home/<USER>/ \"(app-pages-browser)/./components/home/<USER>")));\n_c2 = CategoryTabs;\nconst ProductCategories = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1___default().lazy(()=>__webpack_require__.e(/*! import() */ \"_app-pages-browser_components_home_ProductCategories_tsx\").then(__webpack_require__.bind(__webpack_require__, /*! @/components/home/<USER>/ \"(app-pages-browser)/./components/home/<USER>")));\n_c3 = ProductCategories;\nconst Homepage = ()=>{\n    _s();\n    const { toast } = (0,_components_ui_use_toast__WEBPACK_IMPORTED_MODULE_2__.useToast)();\n    // Use a single API instance for all API calls\n    const { data, read } = (0,_hooks_useApi__WEBPACK_IMPORTED_MODULE_4__[\"default\"])(_constant_urls__WEBPACK_IMPORTED_MODULE_3__.MAIN_URL || '');\n    // Create state variables to store different data types\n    const [futureProduct, setFutureProduct] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [futureProductLoading, setFutureProductLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [popularProduct, setPopularProduct] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [popularProductLoading, setPopularProductLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [categories, setCategories] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [categoriesLoading, setCategoriesLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Use refs to track if data has been fetched\n    const initialDataFetchedRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(false);\n    const [categoryProducts, setCategoryProducts] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    // Track if category products have been fetched\n    const categoryProductsFetchedRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(false);\n    // Function to fetch products for each category - optimized to prevent continuous API calls\n    const fetchCategoryProducts = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"Homepage.useCallback[fetchCategoryProducts]\": async ()=>{\n            // Skip if categories aren't loaded yet\n            if (!Array.isArray(categories) || categories.length === 0) return;\n            // Skip if we've already fetched and have data\n            if (categoryProductsFetchedRef.current && categoryProducts.length > 0 && categoryProducts.every({\n                \"Homepage.useCallback[fetchCategoryProducts]\": (cat)=>!cat.loading\n            }[\"Homepage.useCallback[fetchCategoryProducts]\"])) {\n                return;\n            }\n            // Limit to first 6 categories to avoid overwhelming the page and reduce API calls\n            const limitedCategories = categories.slice(0, 6);\n            // Set initial loading state\n            if (categoryProducts.length === 0) {\n                const initialCategoryProducts = limitedCategories.map({\n                    \"Homepage.useCallback[fetchCategoryProducts].initialCategoryProducts\": (category)=>({\n                            category,\n                            products: [],\n                            loading: true\n                        })\n                }[\"Homepage.useCallback[fetchCategoryProducts].initialCategoryProducts\"]);\n                setCategoryProducts(initialCategoryProducts);\n            }\n            // Fetch products for each category\n            const promises = limitedCategories.map({\n                \"Homepage.useCallback[fetchCategoryProducts].promises\": async (category, index)=>{\n                    try {\n                        var _result_results;\n                        // Use the single read function from our consolidated API instance\n                        const result = await read(\"\".concat((0,_constant_urls__WEBPACK_IMPORTED_MODULE_3__.CATEGORIZE_PRODUCTS)(category.slug), \"?page_size=8\"));\n                        return {\n                            index,\n                            category,\n                            products: (result === null || result === void 0 ? void 0 : (_result_results = result.results) === null || _result_results === void 0 ? void 0 : _result_results.products) || [],\n                            success: true\n                        };\n                    } catch (error) {\n                        console.error(\"Error fetching products for \".concat(category.name, \":\"), error);\n                        return {\n                            index,\n                            category,\n                            products: [],\n                            success: false\n                        };\n                    }\n                }\n            }[\"Homepage.useCallback[fetchCategoryProducts].promises\"]);\n            // Wait for all promises to resolve\n            const results = await Promise.all(promises);\n            // Update state once with all results\n            setCategoryProducts({\n                \"Homepage.useCallback[fetchCategoryProducts]\": (prev)=>{\n                    // Start with previous state or empty array\n                    const newState = prev.length > 0 ? [\n                        ...prev\n                    ] : limitedCategories.map({\n                        \"Homepage.useCallback[fetchCategoryProducts]\": (cat)=>({\n                                category: cat,\n                                products: [],\n                                loading: true\n                            })\n                    }[\"Homepage.useCallback[fetchCategoryProducts]\"]);\n                    // Update with new results\n                    results.forEach({\n                        \"Homepage.useCallback[fetchCategoryProducts]\": (result)=>{\n                            if (newState[result.index]) {\n                                newState[result.index] = {\n                                    ...newState[result.index],\n                                    products: result.products,\n                                    loading: false\n                                };\n                            }\n                        }\n                    }[\"Homepage.useCallback[fetchCategoryProducts]\"]);\n                    // Mark as fetched\n                    categoryProductsFetchedRef.current = true;\n                    return newState;\n                }\n            }[\"Homepage.useCallback[fetchCategoryProducts]\"]);\n        }\n    }[\"Homepage.useCallback[fetchCategoryProducts]\"], [\n        categories,\n        read\n    ]);\n    // Load all initial data with a single useEffect to reduce renders and prevent continuous API calls\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Homepage.useEffect\": ()=>{\n            // Skip if we've already loaded the data\n            if (initialDataFetchedRef.current) return;\n            // Create a flag to track if the component is still mounted\n            let isMounted = true;\n            const loadInitialData = {\n                \"Homepage.useEffect.loadInitialData\": async ()=>{\n                    try {\n                        setCategoriesLoading(true);\n                        setFutureProductLoading(true);\n                        setPopularProductLoading(true);\n                        // Load categories first\n                        const categoriesResult = await read(_constant_urls__WEBPACK_IMPORTED_MODULE_3__.CATEGORIES);\n                        // Only continue if component is still mounted\n                        if (!isMounted) return;\n                        // Update categories state\n                        if (categoriesResult) {\n                            setCategories(categoriesResult);\n                        }\n                        setCategoriesLoading(false);\n                        // Load featured and popular products in parallel\n                        const [featuredResult, popularResult] = await Promise.all([\n                            read(_constant_urls__WEBPACK_IMPORTED_MODULE_3__.FUTURED_PRODUCTS),\n                            read(_constant_urls__WEBPACK_IMPORTED_MODULE_3__.PRODUCTS + '?page_size=10') // Fetch 10 products for the Discover section\n                        ]);\n                        if (!isMounted) return;\n                        // Update featured products state\n                        if (featuredResult) {\n                            setFutureProduct(featuredResult);\n                        }\n                        // Update popular products state\n                        if (popularResult) {\n                            setPopularProduct(popularResult);\n                        }\n                        // Mark as fetched to prevent duplicate API calls\n                        initialDataFetchedRef.current = true;\n                        setFutureProductLoading(false);\n                        setPopularProductLoading(false);\n                    } catch (error) {\n                        console.error(\"Error loading initial data:\", error);\n                        setCategoriesLoading(false);\n                        setFutureProductLoading(false);\n                        setPopularProductLoading(false);\n                    }\n                }\n            }[\"Homepage.useEffect.loadInitialData\"];\n            loadInitialData();\n            // Cleanup function to prevent state updates after unmount\n            return ({\n                \"Homepage.useEffect\": ()=>{\n                    isMounted = false;\n                }\n            })[\"Homepage.useEffect\"];\n        }\n    }[\"Homepage.useEffect\"], [\n        read\n    ]);\n    // Fetch products for each category when categories are loaded\n    // This useEffect now depends only on categories and fetchCategoryProducts\n    // It will only run when categories change, not on every render\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Homepage.useEffect\": ()=>{\n            if (categories && categories.length > 0 && !categoryProductsFetchedRef.current) {\n                fetchCategoryProducts();\n            }\n        }\n    }[\"Homepage.useEffect\"], [\n        fetchCategoryProducts,\n        categories\n    ]);\n    // We no longer need featuredProductsForHero since we're using HeroCarousel\n    // Get featured products for product section - memoized to prevent recalculations\n    const featuredProducts = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)({\n        \"Homepage.useMemo[featuredProducts]\": ()=>{\n            if (futureProduct && Array.isArray(futureProduct)) {\n                return futureProduct;\n            } else if (futureProduct && futureProduct.results && Array.isArray(futureProduct.results)) {\n                return futureProduct.results;\n            } else if (futureProduct && futureProduct.products && Array.isArray(futureProduct.products)) {\n                return futureProduct.products;\n            }\n            // Return empty array if no products are available - no more static fallback\n            return [];\n        }\n    }[\"Homepage.useMemo[featuredProducts]\"], [\n        futureProduct\n    ]);\n    // Get popular products - memoized to prevent recalculations\n    const popularProducts = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)({\n        \"Homepage.useMemo[popularProducts]\": ()=>{\n            // First check our dedicated popularProduct state\n            if (popularProduct && Array.isArray(popularProduct)) {\n                return popularProduct;\n            } else if (popularProduct && popularProduct.results && Array.isArray(popularProduct.results)) {\n                return popularProduct.results;\n            } else if (popularProduct && popularProduct.products && Array.isArray(popularProduct.products)) {\n                return popularProduct.products;\n            }\n            // Fallback to data from the main API call if popularProduct isn't available\n            if (data && Array.isArray(data)) {\n                return data;\n            } else if (data && data.results && Array.isArray(data.results)) {\n                return data.results;\n            } else if (data && data.products && Array.isArray(data.products)) {\n                return data.products;\n            }\n            // Return empty array if no products are available - no more static fallback\n            return [];\n        }\n    }[\"Homepage.useMemo[popularProducts]\"], [\n        popularProduct,\n        data\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"bg-theme-homepage w-full relative overflow-hidden\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute top-0 left-0 w-full h-full overflow-hidden pointer-events-none\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute top-0 left-0 w-full h-full bg-gradient-to-br from-blue-50/30 via-purple-50/20 to-emerald-50/30\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Triumph\\\\ecommerce\\\\app\\\\page.tsx\",\n                        lineNumber: 266,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute top-[10%] right-[-10%] w-[500px] h-[500px] rounded-full bg-gradient-to-br from-theme-accent-secondary/20 to-theme-accent-primary/10 blur-3xl animate-pulse-soft\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Triumph\\\\ecommerce\\\\app\\\\page.tsx\",\n                        lineNumber: 269,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute top-[40%] left-[-10%] w-[600px] h-[600px] rounded-full bg-gradient-to-tr from-theme-accent-primary/15 to-blue-400/10 blur-3xl animate-float\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Triumph\\\\ecommerce\\\\app\\\\page.tsx\",\n                        lineNumber: 270,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute bottom-[5%] right-[5%] w-[400px] h-[400px] rounded-full bg-gradient-to-bl from-purple-400/15 to-theme-accent-secondary/10 blur-3xl animate-pulse-soft\",\n                        style: {\n                            animationDelay: '1s'\n                        }\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Triumph\\\\ecommerce\\\\app\\\\page.tsx\",\n                        lineNumber: 271,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute top-[20%] left-[20%] w-2 h-2 bg-theme-accent-primary/30 rounded-full animate-float\",\n                        style: {\n                            animationDelay: '0.5s'\n                        }\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Triumph\\\\ecommerce\\\\app\\\\page.tsx\",\n                        lineNumber: 274,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute top-[60%] right-[30%] w-3 h-3 bg-theme-accent-secondary/40 rounded-full animate-float\",\n                        style: {\n                            animationDelay: '1.5s'\n                        }\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Triumph\\\\ecommerce\\\\app\\\\page.tsx\",\n                        lineNumber: 275,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute top-[80%] left-[40%] w-1.5 h-1.5 bg-blue-400/50 rounded-full animate-float\",\n                        style: {\n                            animationDelay: '2s'\n                        }\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Triumph\\\\ecommerce\\\\app\\\\page.tsx\",\n                        lineNumber: 276,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute top-[30%] right-[60%] w-2.5 h-2.5 bg-purple-400/40 rounded-full animate-float\",\n                        style: {\n                            animationDelay: '0.8s'\n                        }\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Triumph\\\\ecommerce\\\\app\\\\page.tsx\",\n                        lineNumber: 277,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute top-[15%] left-[10%] w-20 h-20 border border-theme-accent-primary/20 rotate-45 animate-spin\",\n                        style: {\n                            animationDuration: '20s'\n                        }\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Triumph\\\\ecommerce\\\\app\\\\page.tsx\",\n                        lineNumber: 280,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute bottom-[20%] right-[15%] w-16 h-16 border border-theme-accent-secondary/20 rotate-12 animate-spin\",\n                        style: {\n                            animationDuration: '25s',\n                            animationDirection: 'reverse'\n                        }\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Triumph\\\\ecommerce\\\\app\\\\page.tsx\",\n                        lineNumber: 281,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0 opacity-[0.02] bg-[radial-gradient(#000_1px,transparent_1px)] bg-[size:50px_50px]\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Triumph\\\\ecommerce\\\\app\\\\page.tsx\",\n                        lineNumber: 284,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\Triumph\\\\ecommerce\\\\app\\\\page.tsx\",\n                lineNumber: 264,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_layout_MainHOF__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"min-h-screen flex flex-col w-full relative z-10\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"relative z-20 animate-slide-in-mobile\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"backdrop-blur-sm bg-white/5 rounded-xl mx-2 sm:mx-4 mt-2 shadow-lg border border-white/10\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ProductCategories, {\n                                    categories: categories !== null && categories !== void 0 ? categories : [],\n                                    variant: \"navigation\",\n                                    showTitle: false,\n                                    showViewAll: false,\n                                    maxCategories: 12,\n                                    accentColor: \"primary\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Triumph\\\\ecommerce\\\\app\\\\page.tsx\",\n                                    lineNumber: 293,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Triumph\\\\ecommerce\\\\app\\\\page.tsx\",\n                                lineNumber: 292,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Triumph\\\\ecommerce\\\\app\\\\page.tsx\",\n                            lineNumber: 291,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ClientOnly__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((react__WEBPACK_IMPORTED_MODULE_1___default().Suspense), {\n                                fallback: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-full space-y-8 px-2 sm:px-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-full h-[400px] bg-gradient-to-r from-gray-100 to-gray-50 animate-shine rounded-xl shadow-lg\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Triumph\\\\ecommerce\\\\app\\\\page.tsx\",\n                                            lineNumber: 308,\n                                            columnNumber: 17\n                                        }, void 0),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-full h-[300px] bg-gradient-to-r from-gray-100 to-gray-50 animate-shine rounded-xl shadow-lg\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Triumph\\\\ecommerce\\\\app\\\\page.tsx\",\n                                            lineNumber: 309,\n                                            columnNumber: 17\n                                        }, void 0),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-full h-[300px] bg-gradient-to-r from-gray-100 to-gray-50 animate-shine rounded-xl shadow-lg\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Triumph\\\\ecommerce\\\\app\\\\page.tsx\",\n                                            lineNumber: 310,\n                                            columnNumber: 17\n                                        }, void 0)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Triumph\\\\ecommerce\\\\app\\\\page.tsx\",\n                                    lineNumber: 307,\n                                    columnNumber: 15\n                                }, void 0),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                                        className: \"relative w-full fade-in-professional\",\n                                        style: {\n                                            animationDelay: '0.2s'\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"transform transition-all duration-700 hover:scale-[1.01]\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(HeroCarousel, {}, void 0, false, {\n                                                    fileName: \"D:\\\\Triumph\\\\ecommerce\\\\app\\\\page.tsx\",\n                                                    lineNumber: 316,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Triumph\\\\ecommerce\\\\app\\\\page.tsx\",\n                                                lineNumber: 315,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"relative z-10 mt-6 mb-8 fade-in-professional\",\n                                                style: {\n                                                    animationDelay: '0.4s'\n                                                },\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_TrustIndicators__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {}, void 0, false, {\n                                                    fileName: \"D:\\\\Triumph\\\\ecommerce\\\\app\\\\page.tsx\",\n                                                    lineNumber: 319,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Triumph\\\\ecommerce\\\\app\\\\page.tsx\",\n                                                lineNumber: 318,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Triumph\\\\ecommerce\\\\app\\\\page.tsx\",\n                                        lineNumber: 314,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"fade-in-professional\",\n                                        style: {\n                                            animationDelay: '0.6s'\n                                        },\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"backdrop-blur-sm bg-white/5 rounded-2xl mx-2 sm:mx-4 p-4 sm:p-6 shadow-lg border border-white/10 hover:shadow-xl transition-all duration-500\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ProductSection, {\n                                                title: \"Featured Products\",\n                                                subtitle: \"Discover our handpicked selection of premium products\",\n                                                products: featuredProducts,\n                                                loading: futureProductLoading,\n                                                viewAllLink: \"/shop\",\n                                                accentColor: \"primary\",\n                                                columns: {\n                                                    xs: 2,\n                                                    sm: 2,\n                                                    md: 3,\n                                                    lg: 4,\n                                                    xl: 4,\n                                                    \"2xl\": 5\n                                                }\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Triumph\\\\ecommerce\\\\app\\\\page.tsx\",\n                                                lineNumber: 326,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Triumph\\\\ecommerce\\\\app\\\\page.tsx\",\n                                            lineNumber: 325,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Triumph\\\\ecommerce\\\\app\\\\page.tsx\",\n                                        lineNumber: 324,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"fade-in-professional\",\n                                        style: {\n                                            animationDelay: '0.8s'\n                                        },\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"backdrop-blur-sm bg-white/5 rounded-2xl mx-2 sm:mx-4 p-4 sm:p-6 shadow-lg border border-white/10 hover:shadow-xl transition-all duration-500\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ProductSection, {\n                                                title: \"Discover Products\",\n                                                subtitle: \"Explore our most popular items\",\n                                                products: popularProducts,\n                                                loading: popularProductLoading,\n                                                viewAllLink: \"/shop\",\n                                                accentColor: \"secondary\",\n                                                columns: {\n                                                    xs: 2,\n                                                    sm: 2,\n                                                    md: 3,\n                                                    lg: 4,\n                                                    xl: 4,\n                                                    \"2xl\": 5\n                                                }\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Triumph\\\\ecommerce\\\\app\\\\page.tsx\",\n                                                lineNumber: 348,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Triumph\\\\ecommerce\\\\app\\\\page.tsx\",\n                                            lineNumber: 347,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Triumph\\\\ecommerce\\\\app\\\\page.tsx\",\n                                        lineNumber: 346,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"fade-in-professional\",\n                                        style: {\n                                            animationDelay: '1.0s'\n                                        },\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"backdrop-blur-sm bg-gradient-to-r from-white/5 to-white/10 rounded-2xl mx-2 sm:mx-4 p-4 sm:p-6 shadow-lg border border-white/10 hover:shadow-xl transition-all duration-500\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ProductCategories, {\n                                                title: \"Shop by Category\",\n                                                subtitle: \"Browse our collection by category\",\n                                                categories: categories || [],\n                                                accentColor: \"tertiary\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Triumph\\\\ecommerce\\\\app\\\\page.tsx\",\n                                                lineNumber: 370,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Triumph\\\\ecommerce\\\\app\\\\page.tsx\",\n                                            lineNumber: 369,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Triumph\\\\ecommerce\\\\app\\\\page.tsx\",\n                                        lineNumber: 368,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    categoryProducts.some((cat)=>cat.products && cat.products.length > 0) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"fade-in-professional\",\n                                        style: {\n                                            animationDelay: '1.2s'\n                                        },\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"backdrop-blur-sm bg-gradient-to-l from-white/5 to-white/10 rounded-2xl mx-2 sm:mx-4 p-4 sm:p-6 shadow-lg border border-white/10 hover:shadow-xl transition-all duration-500\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CategoryTabs, {\n                                                title: \"Browse Products by Category\",\n                                                subtitle: \"Filter products by your favorite categories\",\n                                                categories: categories || [],\n                                                categoryProducts: categoryProducts.filter((cat)=>cat.products && cat.products.length > 0),\n                                                accentColor: \"primary\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Triumph\\\\ecommerce\\\\app\\\\page.tsx\",\n                                                lineNumber: 383,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Triumph\\\\ecommerce\\\\app\\\\page.tsx\",\n                                            lineNumber: 382,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Triumph\\\\ecommerce\\\\app\\\\page.tsx\",\n                                        lineNumber: 381,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    categoryProducts.map((categoryData, categoryIndex)=>// Only show categories with at least 4 products\n                                        Array.isArray(categoryData.products) && categoryData.products.length >= 4 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"fade-in-professional\",\n                                            style: {\n                                                animationDelay: \"\".concat(1.4 + categoryIndex * 0.2, \"s\")\n                                            },\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"backdrop-blur-sm bg-gradient-to-br from-white/5 to-white/8 rounded-2xl mx-2 sm:mx-4 p-4 sm:p-6 shadow-lg border border-white/10 hover:shadow-xl transition-all duration-500 hover:scale-[1.01]\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ProductSection, {\n                                                    title: categoryData.category.name,\n                                                    products: categoryData.products,\n                                                    loading: categoryData.loading,\n                                                    viewAllLink: \"/shop?category=\".concat(categoryData.category.slug),\n                                                    accentColor: categoryIndex % 3 === 0 ? \"primary\" : categoryIndex % 3 === 1 ? \"secondary\" : \"tertiary\",\n                                                    columns: {\n                                                        xs: 2,\n                                                        sm: 2,\n                                                        md: 3,\n                                                        lg: 4,\n                                                        xl: 4,\n                                                        \"2xl\": 5\n                                                    }\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Triumph\\\\ecommerce\\\\app\\\\page.tsx\",\n                                                    lineNumber: 404,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Triumph\\\\ecommerce\\\\app\\\\page.tsx\",\n                                                lineNumber: 403,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, categoryData.category.id, false, {\n                                            fileName: \"D:\\\\Triumph\\\\ecommerce\\\\app\\\\page.tsx\",\n                                            lineNumber: 398,\n                                            columnNumber: 17\n                                        }, undefined) : null)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Triumph\\\\ecommerce\\\\app\\\\page.tsx\",\n                                lineNumber: 306,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Triumph\\\\ecommerce\\\\app\\\\page.tsx\",\n                            lineNumber: 305,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Triumph\\\\ecommerce\\\\app\\\\page.tsx\",\n                    lineNumber: 289,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"D:\\\\Triumph\\\\ecommerce\\\\app\\\\page.tsx\",\n                lineNumber: 288,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\Triumph\\\\ecommerce\\\\app\\\\page.tsx\",\n        lineNumber: 262,\n        columnNumber: 5\n    }, undefined);\n};\n_s(Homepage, \"t1NL8oCH1x/iqJpW5zHQdihbFlM=\", false, function() {\n    return [\n        _components_ui_use_toast__WEBPACK_IMPORTED_MODULE_2__.useToast,\n        _hooks_useApi__WEBPACK_IMPORTED_MODULE_4__[\"default\"]\n    ];\n});\n_c4 = Homepage;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Homepage);\nvar _c, _c1, _c2, _c3, _c4;\n$RefreshReg$(_c, \"HeroCarousel\");\n$RefreshReg$(_c1, \"ProductSection\");\n$RefreshReg$(_c2, \"CategoryTabs\");\n$RefreshReg$(_c3, \"ProductCategories\");\n$RefreshReg$(_c4, \"Homepage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/page.tsx\n"));

/***/ })

});