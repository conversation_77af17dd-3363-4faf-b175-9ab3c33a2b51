@tailwind base;
@tailwind components;
@tailwind utilities;

#root {
  width: 100%;
  max-width: 100%;
  margin: 0 auto;
  padding: 0;
  text-align: center;
}

.logo {
  height: 6em;
  padding: 1.5em;
  will-change: filter;
  transition: filter 300ms;
}
.logo:hover {
  filter: drop-shadow(0 0 2em #646cffaa);
}
.logo.react:hover {
  filter: drop-shadow(0 0 2em #61dafbaa);
}

@keyframes logo-spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

@media (prefers-reduced-motion: no-preference) {
  a:nth-of-type(2) .logo {
    animation: logo-spin infinite 20s linear;
  }
}

.card {
  padding: 2em;
}

.read-the-docs {
  color: #888;
}

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 0 0% 3.9%;

    --card: 0 0% 100%;
    --card-foreground: 0 0% 3.9%;

    --popover: 0 0% 100%;
    --popover-foreground: 0 0% 3.9%;

    --primary: 0 0% 9%;
    --primary-foreground: 0 0% 98%;

    --secondary: 0 0% 96.1%;
    --secondary-foreground: 0 0% 9%;

    --muted: 0 0% 96.1%;
    --muted-foreground: 0 0% 45.1%;

    --accent: 0 0% 96.1%;
    --accent-foreground: 0 0% 9%;

    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 0 0% 98%;

    --border: 0 0% 89.8%;
    --input: 0 0% 89.8%;
    --ring: 0 0% 3.9%;

    --radius: 0.5rem;

    --chart-1: 12 76% 61%;
    --chart-2: 173 58% 39%;
    --chart-3: 197 37% 24%;
    --chart-4: 43 74% 66%;
    --chart-5: 27 87% 67%;

    /* Smart Home Theme Colors */
    --theme-header: 222 30% 16%; /* #1A1F36 Midnight Blue */
    --theme-footer: 222 30% 16%; /* #1A1F36 Midnight Blue */
    --theme-homepage: 0 0% 98%; /* #FAFAFA Pearl White */
    --theme-text-primary: 210 29% 24%; /* #2C3E50 Charcoal */
    --theme-text-secondary: 204 8% 76%; /* #BDC3C7 Light Gray */
    --theme-accent-primary: 145 63% 49%; /* #2ECC71 Emerald Green */
    --theme-accent-hover: 145 65% 42%; /* #27AE60 Darker Green */
    --theme-accent-secondary: 37 87% 69%; /* #F5C469 Soft Gold */
    --theme-out-of-stock: 6 78% 57%; /* #E74C3C Coral Red */
  }

  .dark {
    --background: 0 0% 3.9%;
    --foreground: 0 0% 98%;

    --card: 0 0% 3.9%;
    --card-foreground: 0 0% 98%;

    --popover: 0 0% 3.9%;
    --popover-foreground: 0 0% 98%;

    --primary: 0 0% 98%;
    --primary-foreground: 0 0% 9%;

    --secondary: 0 0% 14.9%;
    --secondary-foreground: 0 0% 98%;

    --muted: 0 0% 14.9%;
    --muted-foreground: 0 0% 63.9%;

    --accent: 0 0% 14.9%;
    --accent-foreground: 0 0% 98%;

    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 0 0% 98%;

    --border: 0 0% 14.9%;
    --input: 0 0% 14.9%;
    --ring: 0 0% 83.1%;
    --chart-1: 220 70% 50%;
    --chart-2: 160 60% 45%;
    --chart-3: 30 80% 55%;
    --chart-4: 280 65% 60%;
    --chart-5: 340 75% 55%;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground w-full max-w-full overflow-x-hidden min-h-screen;
    text-rendering: optimizeLegibility;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
  }

  html {
    @apply w-full max-w-full overflow-x-hidden scroll-smooth;
  }

  /* Responsive typography */
  h1 {
    @apply text-2xl xs:text-2xl sm:text-3xl md:text-4xl font-bold;
  }

  h2 {
    @apply text-xl xs:text-xl sm:text-2xl md:text-3xl font-bold;
  }

  h3 {
    @apply text-lg xs:text-lg sm:text-xl md:text-2xl font-semibold;
  }

  p {
    @apply text-sm xs:text-sm sm:text-base;
  }
}

/* Responsive utilities */
@layer utilities {
  .text-shadow-sm {
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
  }

  .text-shadow {
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  }

  .text-shadow-md {
    text-shadow: 0 4px 8px rgba(0, 0, 0, 0.12);
  }

  .text-shadow-lg {
    text-shadow: 0 8px 16px rgba(0, 0, 0, 0.15);
  }

  .text-shadow-none {
    text-shadow: none;
  }

  /* Responsive padding utilities */
  .responsive-x-padding {
    @apply px-4 sm:px-6 md:px-8 lg:px-10;
  }

  .responsive-y-padding {
    @apply py-4 sm:py-6 md:py-8 lg:py-10;
  }

  /* Scrollbar utilities */
  .scrollbar-hide {
    -ms-overflow-style: none;  /* Internet Explorer 10+ */
    scrollbar-width: none;  /* Firefox */
  }

  .scrollbar-hide::-webkit-scrollbar {
    display: none;  /* Safari and Chrome */
  }

  /* Professional category navigation animations */
  .category-card-professional {
    position: relative;
    transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
  }

  .category-card-professional::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(255,255,255,0.1) 0%, rgba(255,255,255,0.05) 100%);
    border-radius: inherit;
    opacity: 0;
    transition: opacity 0.3s ease;
  }

  .category-card-professional:hover::before {
    opacity: 1;
  }

  /* Smooth lift animation */
  .category-lift {
    transform: translateY(0) scale(1);
    transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
  }

  .category-lift:hover {
    transform: translateY(-8px) scale(1.02);
  }

  /* Professional shadow effects */
  .shadow-professional {
    box-shadow:
      0 4px 6px -1px rgba(0, 0, 0, 0.1),
      0 2px 4px -1px rgba(0, 0, 0, 0.06),
      0 0 0 1px rgba(255, 255, 255, 0.05) inset;
  }

  .shadow-professional-hover {
    box-shadow:
      0 20px 25px -5px rgba(0, 0, 0, 0.1),
      0 10px 10px -5px rgba(0, 0, 0, 0.04),
      0 0 0 1px rgba(255, 255, 255, 0.05) inset;
  }

  /* Gradient text effect */
  .text-gradient-professional {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
  }

  /* Professional skeleton animation */
  .skeleton-professional {
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
    background-size: 200% 100%;
    animation: shimmer-professional 2s infinite;
  }

  @keyframes shimmer-professional {
    0% {
      background-position: -200% 0;
    }
    100% {
      background-position: 200% 0;
    }
  }

  /* Smooth fade-in animation */
  .fade-in-professional {
    animation: fadeInProfessional 0.6s ease-out forwards;
  }

  @keyframes fadeInProfessional {
    from {
      opacity: 0;
      transform: translateY(20px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }
}

/* scroll bar style  */
/* Style the entire scrollbar */
::-webkit-scrollbar {
  width: 6px; /* Width of the scrollbar */
  height: 6px; /* Height for horizontal scrollbars */
}

/* Style the scrollbar track (background) */
::-webkit-scrollbar-track {
 display: none;
}

/* Style the scrollbar thumb (draggable handle) */
::-webkit-scrollbar-thumb {
  background: #888; /* Darker gray handle */
  border-radius: 10px; /* Rounded corners */
  border: 2px solid #f0f0f0; /* Adds padding and matches track background */
}

/* Hover effect on the scrollbar thumb */
::-webkit-scrollbar-thumb:hover {
  background: #555; /* Darker gray when hovered */
}


/* Original glass morphism - keeping for compatibility */
.glass-morphism-original {
  @apply bg-white/10 backdrop-blur-md border border-white/20;
}

.neo-button {
  @apply relative overflow-hidden transition-all duration-300
         before:absolute before:inset-0 before:bg-black/5
         before:transition-transform before:duration-300
         hover:before:scale-105 active:scale-95;
}

.text-balance {
  text-wrap: balance;
}

.slide-content {
  @apply absolute inset-0 flex flex-col items-center justify-center text-white;
  background: linear-gradient(to bottom, transparent 0%, rgba(0, 0, 0, 0.5) 100%);
}

/* Enhanced Custom animations */
@keyframes float {
  0% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-15px);
  }
  100% {
    transform: translateY(0px);
  }
}

@keyframes pulse-soft {
  0% {
    box-shadow: 0 0 0 0 rgba(46, 204, 113, 0.4);
    transform: scale(1);
  }
  70% {
    box-shadow: 0 0 0 15px rgba(46, 204, 113, 0);
    transform: scale(1.05);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(46, 204, 113, 0);
    transform: scale(1);
  }
}

@keyframes shine {
  0% {
    background-position: -200% center;
  }
  100% {
    background-position: 200% center;
  }
}

@keyframes glow {
  0% {
    box-shadow: 0 0 5px rgba(46, 204, 113, 0.2);
  }
  50% {
    box-shadow: 0 0 25px rgba(46, 204, 113, 0.4), 0 0 35px rgba(46, 204, 113, 0.2);
  }
  100% {
    box-shadow: 0 0 5px rgba(46, 204, 113, 0.2);
  }
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(40px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInDown {
  from {
    opacity: 0;
    transform: translateY(-40px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInLeft {
  from {
    opacity: 0;
    transform: translateX(-40px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(40px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes fadeInScale {
  from {
    opacity: 0;
    transform: scale(0.9);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes rotateIn {
  from {
    opacity: 0;
    transform: rotate(-10deg) scale(0.9);
  }
  to {
    opacity: 1;
    transform: rotate(0deg) scale(1);
  }
}

@keyframes gradient-shift {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

@keyframes shimmer {
  0% {
    transform: translateX(-100%);
  }
  100% {
    transform: translateX(100%);
  }
}

.animate-float {
  animation: float 6s ease-in-out infinite;
}

.animate-pulse-soft {
  animation: pulse-soft 3s ease-in-out infinite;
}

.animate-shine {
  background: linear-gradient(90deg, transparent, rgba(255,255,255,0.4), transparent);
  background-size: 200% 100%;
  animation: shine 2s linear infinite;
}

.animate-glow {
  animation: glow 2s ease-in-out infinite;
}

.animate-slide-in-up {
  animation: slideInUp 0.6s ease-out forwards;
}

.animate-slide-in-down {
  animation: slideInDown 0.6s ease-out forwards;
}

.animate-slide-in-left {
  animation: slideInLeft 0.6s ease-out forwards;
}

.animate-slide-in-right {
  animation: slideInRight 0.6s ease-out forwards;
}

.animate-fade-in-scale {
  animation: fadeInScale 0.8s ease-out forwards;
}

.animate-rotate-in {
  animation: rotateIn 0.8s ease-out forwards;
}

.animate-gradient-shift {
  background-size: 400% 400%;
  animation: gradient-shift 8s ease infinite;
}

.animate-shimmer {
  position: relative;
  overflow: hidden;
}

.animate-shimmer::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
  animation: shimmer 2s infinite;
}

/* Enhanced Glass morphism */
.glass-morphism {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.1);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.glass-morphism:hover {
  background: rgba(255, 255, 255, 0.15);
  backdrop-filter: blur(25px);
  -webkit-backdrop-filter: blur(25px);
  border: 1px solid rgba(255, 255, 255, 0.3);
  box-shadow: 0 12px 40px 0 rgba(31, 38, 135, 0.15);
  transform: translateY(-2px);
}

.glass-morphism-strong {
  background: rgba(255, 255, 255, 0.2);
  backdrop-filter: blur(30px);
  -webkit-backdrop-filter: blur(30px);
  border: 1px solid rgba(255, 255, 255, 0.3);
  box-shadow: 0 12px 40px 0 rgba(31, 38, 135, 0.15);
}

/* Enhanced Gradient text */
.gradient-text {
  @apply bg-clip-text text-transparent bg-gradient-to-r from-indigo-600 to-violet-600;
}

.gradient-text-primary {
  @apply bg-clip-text text-transparent bg-gradient-to-r from-theme-accent-primary to-theme-accent-hover;
}

.gradient-text-secondary {
  @apply bg-clip-text text-transparent bg-gradient-to-r from-theme-accent-secondary to-yellow-500;
}

.gradient-text-animated {
  @apply bg-clip-text text-transparent bg-gradient-to-r from-theme-accent-primary via-theme-accent-secondary to-theme-accent-primary;
  background-size: 200% auto;
  animation: gradient-shift 3s ease infinite;
}

/* Mobile Navigation Animations */
@keyframes slide-in-mobile {
  from {
    opacity: 0;
    transform: translateY(-20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slide-out-mobile {
  from {
    opacity: 1;
    transform: translateY(0);
  }
  to {
    opacity: 0;
    transform: translateY(-20px);
  }
}

@keyframes mobile-menu-slide-down {
  from {
    transform: translateY(-100%);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

@keyframes mobile-menu-slide-up {
  from {
    transform: translateY(0);
    opacity: 1;
  }
  to {
    transform: translateY(-100%);
    opacity: 0;
  }
}

.animate-slide-in-mobile {
  animation: slide-in-mobile 0.4s ease-out forwards;
}

/* Enhanced hover effects */
.hover-lift {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.hover-lift:hover {
  transform: translateY(-6px) scale(1.02);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
}

.hover-glow {
  transition: all 0.3s ease;
}

.hover-glow:hover {
  box-shadow: 0 0 30px rgba(46, 204, 113, 0.4);
  transform: scale(1.05);
}

.hover-scale {
  transition: transform 0.3s ease;
}

.hover-scale:hover {
  transform: scale(1.05);
}

.hover-rotate {
  transition: transform 0.3s ease;
}

.hover-rotate:hover {
  transform: rotate(5deg) scale(1.05);
}

.hover-slide {
  transition: transform 0.3s ease;
}

.hover-slide:hover {
  transform: translateX(10px);
}

/* Enhanced shadow utilities */
.shadow-3xl {
  box-shadow: 0 35px 60px -12px rgba(0, 0, 0, 0.25);
}

.shadow-glow-primary {
  box-shadow: 0 0 20px rgba(46, 204, 113, 0.3);
}

.shadow-glow-secondary {
  box-shadow: 0 0 20px rgba(245, 196, 105, 0.3);
}

.shadow-inner-glow {
  box-shadow: inset 0 0 20px rgba(46, 204, 113, 0.1);
}

/* Backdrop blur utilities */
.backdrop-blur-xs {
  backdrop-filter: blur(2px);
  -webkit-backdrop-filter: blur(2px);
}

.backdrop-blur-3xl {
  backdrop-filter: blur(64px);
  -webkit-backdrop-filter: blur(64px);
}

/* Enhanced gradient backgrounds */
.bg-gradient-mesh {
  background:
    radial-gradient(at 40% 20%, hsla(28,100%,74%,0.3) 0px, transparent 50%),
    radial-gradient(at 80% 0%, hsla(189,100%,56%,0.3) 0px, transparent 50%),
    radial-gradient(at 0% 50%, hsla(355,100%,93%,0.3) 0px, transparent 50%),
    radial-gradient(at 80% 50%, hsla(340,100%,76%,0.3) 0px, transparent 50%),
    radial-gradient(at 0% 100%, hsla(22,100%,77%,0.3) 0px, transparent 50%),
    radial-gradient(at 80% 100%, hsla(242,100%,70%,0.3) 0px, transparent 50%),
    radial-gradient(at 0% 0%, hsla(343,100%,76%,0.3) 0px, transparent 50%);
}

.bg-gradient-aurora {
  background: linear-gradient(45deg, #667eea 0%, #764ba2 50%, #667eea 100%);
  background-size: 400% 400%;
  animation: gradient-shift 15s ease infinite;
}

/* Vibrant gradient backgrounds for homepage */
.bg-gradient-vibrant {
  background: linear-gradient(135deg,
    #667eea 0%,
    #764ba2 25%,
    #f093fb 50%,
    #f5576c 75%,
    #4facfe 100%
  );
  background-size: 400% 400%;
  animation: gradient-shift 20s ease infinite;
}

.bg-gradient-cosmic {
  background: linear-gradient(135deg,
    #ff9a9e 0%,
    #fecfef 25%,
    #fecfef 50%,
    #a8edea 75%,
    #fed6e3 100%
  );
  background-size: 400% 400%;
  animation: gradient-shift 25s ease infinite;
}

.bg-gradient-ocean {
  background: linear-gradient(135deg,
    #667eea 0%,
    #764ba2 25%,
    #6B73FF 50%,
    #000DFF 75%,
    #9A9AFF 100%
  );
  background-size: 400% 400%;
  animation: gradient-shift 18s ease infinite;
}

.bg-gradient-sunset {
  background: linear-gradient(135deg,
    #fa709a 0%,
    #fee140 25%,
    #fa709a 50%,
    #fee140 75%,
    #fa709a 100%
  );
  background-size: 400% 400%;
  animation: gradient-shift 22s ease infinite;
}

.bg-gradient-emerald {
  background: linear-gradient(135deg,
    #11998e 0%,
    #38ef7d 25%,
    #11998e 50%,
    #38ef7d 75%,
    #11998e 100%
  );
  background-size: 400% 400%;
  animation: gradient-shift 16s ease infinite;
}

/* Accessibility - Respect prefers-reduced-motion */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
    scroll-behavior: auto !important;
  }

  .animate-float,
  .animate-pulse-soft,
  .animate-glow,
  .animate-gradient-shift,
  .animate-shimmer {
    animation: none !important;
  }
}

.animate-slide-out-mobile {
  animation: slide-out-mobile 0.3s ease-in forwards;
}

.animate-mobile-menu-slide-down {
  animation: mobile-menu-slide-down 0.3s ease-out forwards;
}

.animate-mobile-menu-slide-up {
  animation: mobile-menu-slide-up 0.3s ease-in forwards;
}

/* Enhanced mobile touch targets */
@media (max-width: 768px) {
  .mobile-touch-target {
    min-height: 44px;
    min-width: 44px;
  }

  .mobile-menu-item {
    padding: 16px;
    font-size: 16px;
    line-height: 1.5;
  }
}

/* Smooth transitions for mobile interactions */
.mobile-transition {
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
}

.mobile-transition:active {
  transform: scale(0.95);
}

/* Mobile-specific hover states (for devices that support hover) */
@media (hover: hover) and (pointer: fine) {
  .mobile-hover:hover {
    background-color: rgba(255, 255, 255, 0.1);
  }
}

/* Mobile-specific focus states for accessibility */
.mobile-focus:focus-visible {
  outline: 2px solid #2ECC71;
  outline-offset: 2px;
}

/* Enhanced mobile header styles */
@media (max-width: 768px) {
  .mobile-header-close-btn {
    min-height: 44px;
    min-width: 44px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    transition: all 0.2s ease;
  }

  .mobile-header-close-btn:active {
    transform: scale(0.95);
  }

  .mobile-menu-item-enhanced {
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  }

  .mobile-menu-item-enhanced:hover {
    backdrop-filter: blur(15px);
    border-color: rgba(255, 255, 255, 0.2);
    transform: translateY(-2px);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
  }

  .mobile-search-container {
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.1);
  }

  .mobile-search-input {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
  }

  .mobile-gradient-bg {
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 100%);
  }
}

/* Smooth animations for mobile interactions */
@keyframes mobile-slide-up {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.animate-mobile-slide-up {
  animation: mobile-slide-up 0.3s ease-out forwards;
}

/* Enhanced backdrop blur for mobile overlays */
.mobile-backdrop-enhanced {
  backdrop-filter: blur(8px);
  background: rgba(0, 0, 0, 0.6);
  transition: all 0.3s ease;
}

/* Mobile-optimized button styles */
.mobile-btn-primary {
  background: linear-gradient(135deg, #2ECC71 0%, #27AE60 100%);
  box-shadow: 0 4px 15px rgba(46, 204, 113, 0.3);
  transition: all 0.2s ease;
}

.mobile-btn-primary:hover {
  box-shadow: 0 6px 20px rgba(46, 204, 113, 0.4);
  transform: translateY(-1px);
}

.mobile-btn-primary:active {
  transform: translateY(0);
  box-shadow: 0 2px 10px rgba(46, 204, 113, 0.3);
}

/* Product page mobile improvements */
@media (max-width: 1023px) {
  .product-layout {
    display: flex;
    flex-direction: column;
    gap: 1rem;
  }

  .product-carousel-container {
    position: relative !important;
    top: auto !important;
    margin-bottom: 1rem;
  }

  .product-info-container {
    margin-top: 0;
    padding-top: 0;
  }
}

/* Ensure proper spacing on mobile devices */
@media (max-width: 640px) {
  .product-carousel-container {
    padding: 0.75rem;
  }

  .product-info-container {
    padding: 1rem 0;
  }

  /* Prevent carousel from being too tall on small screens */
  .mobile-carousel-height {
    max-height: 300px;
  }
}

/* Smooth transitions for product page elements */
.product-transition {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Ensure images don't overflow on mobile */
.mobile-image-container {
  overflow: hidden;
  border-radius: 0.5rem;
}

.mobile-image-container img {
  max-width: 100%;
  height: auto;
  object-fit: contain;
}