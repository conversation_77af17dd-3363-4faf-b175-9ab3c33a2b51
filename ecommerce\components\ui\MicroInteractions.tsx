"use client";

import React from 'react';
import { motion } from 'framer-motion';

interface MicroInteractionProps {
  children: React.ReactNode;
  type?: 'hover-lift' | 'hover-scale' | 'hover-glow' | 'hover-rotate' | 'hover-slide' | 'click-bounce';
  className?: string;
  disabled?: boolean;
}

export default function MicroInteraction({
  children,
  type = 'hover-lift',
  className = '',
  disabled = false
}: MicroInteractionProps) {
  
  if (disabled) {
    return <div className={className}>{children}</div>;
  }

  const getAnimationProps = () => {
    switch (type) {
      case 'hover-lift':
        return {
          whileHover: { 
            y: -8, 
            scale: 1.02,
            transition: { duration: 0.2, ease: 'easeOut' }
          },
          whileTap: { 
            y: -4, 
            scale: 1.01,
            transition: { duration: 0.1 }
          }
        };
      
      case 'hover-scale':
        return {
          whileHover: { 
            scale: 1.05,
            transition: { duration: 0.2, ease: 'easeOut' }
          },
          whileTap: { 
            scale: 0.98,
            transition: { duration: 0.1 }
          }
        };
      
      case 'hover-glow':
        return {
          whileHover: { 
            scale: 1.03,
            boxShadow: '0 0 25px rgba(46, 204, 113, 0.4)',
            transition: { duration: 0.2, ease: 'easeOut' }
          },
          whileTap: { 
            scale: 1.01,
            transition: { duration: 0.1 }
          }
        };
      
      case 'hover-rotate':
        return {
          whileHover: { 
            rotate: 3,
            scale: 1.05,
            transition: { duration: 0.2, ease: 'easeOut' }
          },
          whileTap: { 
            rotate: 1,
            scale: 1.02,
            transition: { duration: 0.1 }
          }
        };
      
      case 'hover-slide':
        return {
          whileHover: { 
            x: 8,
            transition: { duration: 0.2, ease: 'easeOut' }
          },
          whileTap: { 
            x: 4,
            transition: { duration: 0.1 }
          }
        };
      
      case 'click-bounce':
        return {
          whileHover: { 
            scale: 1.02,
            transition: { duration: 0.2, ease: 'easeOut' }
          },
          whileTap: { 
            scale: 0.95,
            transition: { duration: 0.1, ease: 'easeInOut' }
          }
        };
      
      default:
        return {};
    }
  };

  return (
    <motion.div
      className={className}
      {...getAnimationProps()}
    >
      {children}
    </motion.div>
  );
}

// Preset components for common interactions
export function HoverLift({ 
  children, 
  className = '' 
}: { 
  children: React.ReactNode; 
  className?: string; 
}) {
  return (
    <MicroInteraction type="hover-lift" className={className}>
      {children}
    </MicroInteraction>
  );
}

export function HoverScale({ 
  children, 
  className = '' 
}: { 
  children: React.ReactNode; 
  className?: string; 
}) {
  return (
    <MicroInteraction type="hover-scale" className={className}>
      {children}
    </MicroInteraction>
  );
}

export function HoverGlow({ 
  children, 
  className = '' 
}: { 
  children: React.ReactNode; 
  className?: string; 
}) {
  return (
    <MicroInteraction type="hover-glow" className={className}>
      {children}
    </MicroInteraction>
  );
}

export function ClickBounce({ 
  children, 
  className = '' 
}: { 
  children: React.ReactNode; 
  className?: string; 
}) {
  return (
    <MicroInteraction type="click-bounce" className={className}>
      {children}
    </MicroInteraction>
  );
}

// Enhanced button component with micro-interactions
export function InteractiveButton({ 
  children, 
  onClick,
  variant = 'primary',
  className = '',
  disabled = false
}: { 
  children: React.ReactNode; 
  onClick?: () => void;
  variant?: 'primary' | 'secondary' | 'ghost';
  className?: string;
  disabled?: boolean;
}) {
  const getVariantStyles = () => {
    switch (variant) {
      case 'primary':
        return 'bg-gradient-to-r from-theme-accent-primary to-theme-accent-hover text-white shadow-lg';
      case 'secondary':
        return 'bg-gradient-to-r from-theme-accent-secondary to-yellow-500 text-white shadow-lg';
      case 'ghost':
        return 'bg-transparent border border-theme-accent-primary text-theme-accent-primary hover:bg-theme-accent-primary hover:text-white';
      default:
        return 'bg-gradient-to-r from-theme-accent-primary to-theme-accent-hover text-white shadow-lg';
    }
  };

  return (
    <motion.button
      className={`
        px-6 py-3 rounded-full font-medium transition-all duration-300
        ${getVariantStyles()}
        ${disabled ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer'}
        ${className}
      `}
      onClick={onClick}
      disabled={disabled}
      whileHover={!disabled ? { 
        scale: 1.05,
        boxShadow: '0 10px 25px rgba(0, 0, 0, 0.2)',
        transition: { duration: 0.2 }
      } : undefined}
      whileTap={!disabled ? { 
        scale: 0.98,
        transition: { duration: 0.1 }
      } : undefined}
    >
      {children}
    </motion.button>
  );
}
