"use client";

import React from 'react';
import AnimatedBackground from '@/components/ui/AnimatedBackground';
import ScrollReveal, { GlassReveal, SlideUpReveal } from '@/components/ui/ScrollReveal';
import ParallaxContainer, { ParallaxFloat, ParallaxFade } from '@/components/ui/ParallaxContainer';
import { HeroLoading, SectionLoading } from '@/components/ui/EnhancedLoading';
import { HoverLift, HoverScale, HoverGlow, InteractiveButton } from '@/components/ui/MicroInteractions';
import { CosmicGradient, OceanGradient, SunsetGradient, VibrantGradient } from '@/components/ui/GradientOverlay';

export default function AnimationTestPage() {
  return (
    <div
      className="min-h-screen"
      style={{
        backgroundImage: 'linear-gradient(-225deg, #473B7B 0%, #3584A7 51%, #30D2BE 100%)',
        backgroundSize: '400% 400%',
        animation: 'gradient-shift 15s ease infinite'
      }}
    >
      <AnimatedBackground variant="hero" className="min-h-screen">
      <div className="container mx-auto px-4 py-8 space-y-12">
        
        {/* Header */}
        <ScrollReveal animation="slideDown" delay={0.1}>
          <div className="text-center">
            <h1 className="text-4xl font-bold gradient-text-animated mb-4">
              Animation Test Page
            </h1>
            <p className="text-lg text-gray-600">
              Testing all enhanced animations and effects
            </p>
          </div>
        </ScrollReveal>

        {/* Glass Morphism Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          <GlassReveal delay={0.2}>
            <div className="p-6">
              <h3 className="text-xl font-semibold mb-3">Glass Morphism</h3>
              <p className="text-gray-600">
                Beautiful glass effect with backdrop blur and subtle transparency.
              </p>
            </div>
          </GlassReveal>

          <GlassReveal delay={0.4}>
            <div className="p-6">
              <h3 className="text-xl font-semibold mb-3">Scroll Reveal</h3>
              <p className="text-gray-600">
                Elements animate into view as you scroll down the page.
              </p>
            </div>
          </GlassReveal>

          <GlassReveal delay={0.6}>
            <div className="p-6">
              <h3 className="text-xl font-semibold mb-3">Staggered Animation</h3>
              <p className="text-gray-600">
                Sequential animations with increasing delays for smooth reveals.
              </p>
            </div>
          </GlassReveal>
        </div>

        {/* Parallax Section */}
        <ParallaxFloat>
          <ScrollReveal animation="scaleIn" delay={0.3}>
            <div className="glass-morphism-strong rounded-2xl p-8 text-center">
              <h2 className="text-3xl font-bold mb-4">Parallax Effects</h2>
              <p className="text-lg text-gray-600">
                This section moves at a different speed as you scroll, creating depth.
              </p>
            </div>
          </ScrollReveal>
        </ParallaxFloat>

        {/* Micro Interactions */}
        <ScrollReveal animation="slideUp" delay={0.2}>
          <div className="glass-morphism rounded-2xl p-8">
            <h2 className="text-2xl font-bold mb-6 text-center">Micro Interactions</h2>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              
              <HoverLift className="glass-morphism p-6 rounded-xl text-center">
                <h4 className="font-semibold mb-2">Hover Lift</h4>
                <p className="text-sm text-gray-600">Lifts up on hover</p>
              </HoverLift>

              <HoverScale className="glass-morphism p-6 rounded-xl text-center">
                <h4 className="font-semibold mb-2">Hover Scale</h4>
                <p className="text-sm text-gray-600">Scales up on hover</p>
              </HoverScale>

              <HoverGlow className="glass-morphism p-6 rounded-xl text-center">
                <h4 className="font-semibold mb-2">Hover Glow</h4>
                <p className="text-sm text-gray-600">Glows on hover</p>
              </HoverGlow>

            </div>
          </div>
        </ScrollReveal>

        {/* Interactive Buttons */}
        <ScrollReveal animation="slideUp" delay={0.4}>
          <div className="glass-morphism rounded-2xl p-8 text-center">
            <h2 className="text-2xl font-bold mb-6">Interactive Buttons</h2>
            <div className="flex flex-wrap justify-center gap-4">
              <InteractiveButton variant="primary">
                Primary Button
              </InteractiveButton>
              <InteractiveButton variant="secondary">
                Secondary Button
              </InteractiveButton>
              <InteractiveButton variant="ghost">
                Ghost Button
              </InteractiveButton>
            </div>
          </div>
        </ScrollReveal>

        {/* Loading Components */}
        <ScrollReveal animation="fadeIn" delay={0.5}>
          <div className="space-y-6">
            <h2 className="text-2xl font-bold text-center">Enhanced Loading States</h2>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <h3 className="text-lg font-semibold mb-3">Hero Loading</h3>
                <HeroLoading />
              </div>
              <div>
                <h3 className="text-lg font-semibold mb-3">Section Loading</h3>
                <SectionLoading />
              </div>
            </div>
          </div>
        </ScrollReveal>

        {/* Parallax Fade Section */}
        <ParallaxFade>
          <ScrollReveal animation="rotateIn" delay={0.3}>
            <div className="glass-morphism rounded-2xl p-8 text-center">
              <h2 className="text-3xl font-bold mb-4">Parallax Fade</h2>
              <p className="text-lg text-gray-600">
                This section fades as you scroll, creating a smooth transition effect.
              </p>
            </div>
          </ScrollReveal>
        </ParallaxFade>

        {/* CSS Animation Classes */}
        <ScrollReveal animation="slideUp" delay={0.6}>
          <div className="glass-morphism rounded-2xl p-8">
            <h2 className="text-2xl font-bold mb-6 text-center">CSS Animations</h2>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
              
              <div className="p-4 bg-white/10 rounded-lg text-center animate-float">
                <div className="w-12 h-12 bg-theme-accent-primary rounded-full mx-auto mb-2"></div>
                <p className="text-sm">Float</p>
              </div>

              <div className="p-4 bg-white/10 rounded-lg text-center animate-pulse-soft">
                <div className="w-12 h-12 bg-theme-accent-secondary rounded-full mx-auto mb-2"></div>
                <p className="text-sm">Pulse Soft</p>
              </div>

              <div className="p-4 bg-white/10 rounded-lg text-center animate-glow">
                <div className="w-12 h-12 bg-theme-accent-primary rounded-full mx-auto mb-2"></div>
                <p className="text-sm">Glow</p>
              </div>

              <div className="p-4 bg-white/10 rounded-lg text-center animate-shimmer">
                <div className="w-12 h-12 bg-gray-300 rounded-full mx-auto mb-2"></div>
                <p className="text-sm">Shimmer</p>
              </div>

            </div>
          </div>
        </ScrollReveal>

        {/* Gradient Showcase */}
        <ScrollReveal animation="slideUp" delay={0.7}>
          <div className="space-y-6">
            <h2 className="text-2xl font-bold text-center text-white">Gradient Backgrounds</h2>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">

              <CosmicGradient intensity="medium" className="p-6 rounded-xl">
                <h3 className="text-lg font-semibold text-white mb-2">Cosmic Gradient</h3>
                <p className="text-white/80">Dreamy cosmic colors with soft transitions</p>
              </CosmicGradient>

              <OceanGradient intensity="medium" className="p-6 rounded-xl">
                <h3 className="text-lg font-semibold text-white mb-2">Ocean Gradient</h3>
                <p className="text-white/80">Deep ocean blues with flowing waves</p>
              </OceanGradient>

              <SunsetGradient intensity="medium" className="p-6 rounded-xl">
                <h3 className="text-lg font-semibold text-white mb-2">Sunset Gradient</h3>
                <p className="text-white/80">Warm sunset colors with golden hues</p>
              </SunsetGradient>

              <VibrantGradient intensity="light" className="p-6 rounded-xl">
                <h3 className="text-lg font-semibold text-white mb-2">Vibrant Gradient</h3>
                <p className="text-white/80">Bold, energetic colors for impact</p>
              </VibrantGradient>

            </div>
          </div>
        </ScrollReveal>

        {/* Footer */}
        <ScrollReveal animation="fadeIn" delay={0.8}>
          <div className="text-center py-8">
            <p className="text-white/80">
              All animations respect <code>prefers-reduced-motion</code> for accessibility.
            </p>
          </div>
        </ScrollReveal>

        </div>
      </AnimatedBackground>
    </div>
  );
}
