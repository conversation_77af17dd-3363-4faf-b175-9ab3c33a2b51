"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("_app-pages-browser_components_home_hero-carousel_tsx",{

/***/ "(app-pages-browser)/./components/home/<USER>":
/*!*************************************************!*\
  !*** ./components/home/<USER>
  \*************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ AmazonStyleCarousel)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/image */ \"(app-pages-browser)/./node_modules/next/dist/api/image.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronLeft_ChevronRight_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronLeft,ChevronRight!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-left.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronLeft_ChevronRight_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronLeft,ChevronRight!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-right.js\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./lib/utils.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\nfunction AmazonStyleCarousel(param) {\n    let { slides, autoplayInterval = 5000, className } = param;\n    _s();\n    const [currentSlide, setCurrentSlide] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [isAutoPlaying, setIsAutoPlaying] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const autoPlayRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const containerRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const [touchStart, setTouchStart] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [touchEnd, setTouchEnd] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // Reset autoplay timer when slide changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AmazonStyleCarousel.useEffect\": ()=>{\n            if (isAutoPlaying) {\n                if (autoPlayRef.current) {\n                    clearInterval(autoPlayRef.current);\n                }\n                autoPlayRef.current = setInterval({\n                    \"AmazonStyleCarousel.useEffect\": ()=>{\n                        setCurrentSlide({\n                            \"AmazonStyleCarousel.useEffect\": (prev)=>(prev + 1) % slides.length\n                        }[\"AmazonStyleCarousel.useEffect\"]);\n                    }\n                }[\"AmazonStyleCarousel.useEffect\"], autoplayInterval);\n            }\n            return ({\n                \"AmazonStyleCarousel.useEffect\": ()=>{\n                    if (autoPlayRef.current) {\n                        clearInterval(autoPlayRef.current);\n                    }\n                }\n            })[\"AmazonStyleCarousel.useEffect\"];\n        }\n    }[\"AmazonStyleCarousel.useEffect\"], [\n        currentSlide,\n        isAutoPlaying,\n        autoplayInterval,\n        slides.length\n    ]);\n    // Pause autoplay on hover\n    const pauseAutoPlay = ()=>setIsAutoPlaying(false);\n    const resumeAutoPlay = ()=>setIsAutoPlaying(true);\n    const goToSlide = (index)=>{\n        setCurrentSlide(index);\n    };\n    const goToPrevSlide = ()=>{\n        setCurrentSlide((prev)=>prev === 0 ? slides.length - 1 : prev - 1);\n    };\n    const goToNextSlide = ()=>{\n        setCurrentSlide((prev)=>(prev + 1) % slides.length);\n    };\n    // Handle touch events for mobile swipe\n    const handleTouchStart = (e)=>{\n        setTouchStart(e.targetTouches[0].clientX);\n    };\n    const handleTouchMove = (e)=>{\n        setTouchEnd(e.targetTouches[0].clientX);\n    };\n    const handleTouchEnd = ()=>{\n        if (!touchStart || !touchEnd) return;\n        const distance = touchStart - touchEnd;\n        const isLeftSwipe = distance > 50;\n        const isRightSwipe = distance < -50;\n        if (isLeftSwipe) {\n            goToNextSlide();\n        } else if (isRightSwipe) {\n            goToPrevSlide();\n        }\n        setTouchStart(null);\n        setTouchEnd(null);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_5__.cn)(\"relative overflow-hidden rounded-xl shadow-2xl mt-2 bg-gradient-to-r from-theme-header via-theme-header/95 to-theme-header/90 hover:shadow-3xl transition-all duration-700\", className),\n        onMouseEnter: pauseAutoPlay,\n        onMouseLeave: resumeAutoPlay,\n        ref: containerRef,\n        onTouchStart: handleTouchStart,\n        onTouchMove: handleTouchMove,\n        onTouchEnd: handleTouchEnd,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute inset-0 bg-opacity-10 z-0 overflow-hidden\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0 bg-gradient-to-br from-blue-600/10 via-purple-600/5 to-emerald-600/10 animate-pulse-soft\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\home\\\\AmazonStyleCarousel.tsx\",\n                        lineNumber: 121,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0 opacity-10 bg-[radial-gradient(#ffffff33_1px,transparent_1px)] bg-[size:20px_20px] animate-pulse\",\n                        style: {\n                            animationDuration: '4s'\n                        }\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\home\\\\AmazonStyleCarousel.tsx\",\n                        lineNumber: 124,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute top-[20%] left-[10%] w-1 h-1 bg-white/30 rounded-full animate-float\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\home\\\\AmazonStyleCarousel.tsx\",\n                        lineNumber: 127,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute top-[60%] right-[15%] w-1.5 h-1.5 bg-white/20 rounded-full animate-float\",\n                        style: {\n                            animationDelay: '1s'\n                        }\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\home\\\\AmazonStyleCarousel.tsx\",\n                        lineNumber: 128,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute bottom-[30%] left-[30%] w-0.5 h-0.5 bg-white/40 rounded-full animate-float\",\n                        style: {\n                            animationDelay: '2s'\n                        }\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\home\\\\AmazonStyleCarousel.tsx\",\n                        lineNumber: 129,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\home\\\\AmazonStyleCarousel.tsx\",\n                lineNumber: 119,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex transition-transform duration-700 ease-out h-[280px] xs:h-[320px] sm:h-[380px] md:h-[420px] lg:h-[480px] xl:h-[520px]\",\n                style: {\n                    transform: \"translateX(-\".concat(currentSlide * 100, \"%)\")\n                },\n                children: slides.map((slide, index)=>{\n                    var _slide_brand, _slide_brand1, _slide_brand2, _slide_brand3, _slide_brand4;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"min-w-full relative group cursor-pointer transform transition-all duration-700 hover:scale-[1.02]\",\n                        onClick: ()=>{\n                            try {\n                                window.location.href = slide.link || \"/shop\";\n                            } catch (error) {\n                                console.error(\"Navigation error:\", error);\n                                window.location.href = \"/shop\";\n                            }\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute inset-0 bg-gradient-to-r from-[#8a6f4d]/25 via-[#b8956f]/15 to-[#d9c3a9]/10 group-hover:from-[#8a6f4d]/30 group-hover:to-[#d9c3a9]/15 transition-all duration-700\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\home\\\\AmazonStyleCarousel.tsx\",\n                                lineNumber: 146,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute inset-0 bg-gradient-to-br from-transparent via-white/5 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-700\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\home\\\\AmazonStyleCarousel.tsx\",\n                                lineNumber: 149,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute top-0 right-0 bottom-0 w-[45%] xs:w-[42%] sm:w-[45%] md:w-[50%] flex items-center justify-center p-2 xs:p-3 sm:p-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"relative w-full h-full max-w-[95%] max-h-[85%] xs:max-h-[90%]\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                        src: slide.image || '/home/<USER>',\n                                        alt: slide.title || 'Product Image',\n                                        fill: true,\n                                        sizes: \"(max-width: 640px) 50vw, (max-width: 768px) 45vw, (max-width: 1024px) 50vw, 50vw\",\n                                        className: \"object-contain object-center\",\n                                        priority: index === 0,\n                                        onError: (e)=>{\n                                            // Fallback to a placeholder if image fails to load\n                                            const imgElement = e.currentTarget;\n                                            if (!imgElement.src.includes('placeholder') && !imgElement.src.includes('image-1.png')) {\n                                                imgElement.src = '/home/<USER>';\n                                            }\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\home\\\\AmazonStyleCarousel.tsx\",\n                                        lineNumber: 154,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\home\\\\AmazonStyleCarousel.tsx\",\n                                    lineNumber: 153,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\home\\\\AmazonStyleCarousel.tsx\",\n                                lineNumber: 152,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute inset-0 bg-gradient-to-r from-black/50 via-black/30 to-transparent\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\home\\\\AmazonStyleCarousel.tsx\",\n                                lineNumber: 173,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute inset-0 bg-[url('/patterns/dot-pattern.svg')] bg-repeat opacity-10\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\home\\\\AmazonStyleCarousel.tsx\",\n                                lineNumber: 176,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute inset-0 flex items-center\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-white p-2 xs:p-3 sm:p-4 md:p-6 lg:p-8 xl:p-12 w-[55%] xs:w-[58%] sm:w-[55%] md:w-[50%] lg:w-[45%] xl:w-[40%]\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-start gap-1.5 xs:gap-2 sm:gap-3 mb-1 xs:mb-1.5 sm:mb-2 md:mb-3\",\n                                            children: [\n                                                slide.brand && typeof slide.brand !== 'string' && (((_slide_brand = slide.brand) === null || _slide_brand === void 0 ? void 0 : _slide_brand.image_url) || ((_slide_brand1 = slide.brand) === null || _slide_brand1 === void 0 ? void 0 : _slide_brand1.image)) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex-shrink-0 mt-0.5\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-6 h-6 xs:w-8 xs:h-8 sm:w-10 sm:h-10 md:w-12 md:h-12 lg:w-14 lg:h-14 overflow-hidden rounded-md border border-white/80 shadow-lg bg-white flex items-center justify-center p-0.5\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                            src: ((_slide_brand2 = slide.brand) === null || _slide_brand2 === void 0 ? void 0 : _slide_brand2.image_url) || \"\".concat(\"http://localhost:8000\" || 0).concat((_slide_brand3 = slide.brand) === null || _slide_brand3 === void 0 ? void 0 : _slide_brand3.image),\n                                                            alt: \"\".concat((_slide_brand4 = slide.brand) === null || _slide_brand4 === void 0 ? void 0 : _slide_brand4.name, \" logo\"),\n                                                            className: \"max-w-full max-h-full object-contain\",\n                                                            onError: (e)=>{\n                                                                // Hide the image on error\n                                                                const imgElement = e.currentTarget;\n                                                                imgElement.style.display = 'none';\n                                                            }\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\home\\\\AmazonStyleCarousel.tsx\",\n                                                            lineNumber: 188,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\home\\\\AmazonStyleCarousel.tsx\",\n                                                        lineNumber: 187,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\home\\\\AmazonStyleCarousel.tsx\",\n                                                    lineNumber: 186,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                    className: \"text-sm xs:text-base sm:text-lg md:text-xl lg:text-2xl xl:text-3xl 2xl:text-4xl font-bold   [text-shadow:_0_1px_3px_rgb(0_0_0_/_50%)] leading-tight flex-1 pr-1\",\n                                                    children: slide.title\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\home\\\\AmazonStyleCarousel.tsx\",\n                                                    lineNumber: 203,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\home\\\\AmazonStyleCarousel.tsx\",\n                                            lineNumber: 183,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xs xs:text-xs sm:text-sm md:text-base lg:text-lg mb-1.5 xs:mb-2 sm:mb-3 md:mb-4 text-white/90   [text-shadow:_0_1px_2px_rgb(0_0_0_/_30%)] line-clamp-2 leading-snug max-w-[95%] break-words\",\n                                            children: slide.subtitle\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\home\\\\AmazonStyleCarousel.tsx\",\n                                            lineNumber: 210,\n                                            columnNumber: 17\n                                        }, this),\n                                        slide.code && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"inline-block bg-white/20 backdrop-blur-sm px-2 xs:px-2.5 sm:px-3 py-0.5 xs:py-1 rounded-full   text-xs xs:text-xs sm:text-sm md:text-base font-medium mb-1.5 xs:mb-2 sm:mb-3   [text-shadow:_0_1px_1px_rgb(0_0_0_/_20%)] whitespace-nowrap\",\n                                            children: slide.code\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\home\\\\AmazonStyleCarousel.tsx\",\n                                            lineNumber: 217,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"mt-1.5 xs:mt-2 sm:mt-3\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_4___default()), {\n                                                href: slide.link || \"/shop\",\n                                                onClick: (e)=>{\n                                                    e.stopPropagation();\n                                                // Additional error handling can be added here if needed\n                                                },\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                    className: \"bg-[#2ECC71] hover:bg-[#27AE60] text-white font-medium   px-2.5 xs:px-3 sm:px-4 md:px-6 py-1 xs:py-1.5 sm:py-2 rounded-full   transition-all duration-300 hover:shadow-lg hover:scale-105   text-xs xs:text-xs sm:text-sm md:text-base whitespace-nowrap\",\n                                                    children: slide.cta || \"Shop Now\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\home\\\\AmazonStyleCarousel.tsx\",\n                                                    lineNumber: 230,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\home\\\\AmazonStyleCarousel.tsx\",\n                                                lineNumber: 226,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\home\\\\AmazonStyleCarousel.tsx\",\n                                            lineNumber: 225,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\home\\\\AmazonStyleCarousel.tsx\",\n                                    lineNumber: 180,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\home\\\\AmazonStyleCarousel.tsx\",\n                                lineNumber: 179,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute bottom-0 left-0 right-[40%] xs:right-[42%] sm:right-[45%] md:right-[50%] p-1.5 xs:p-2 sm:p-3 bg-gradient-to-t from-black/60 to-transparent\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-white text-xs xs:text-xs sm:text-sm\",\n                                    children: slide.specs && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"mb-0.5 [text-shadow:_0_1px_1px_rgb(0_0_0_/_30%)] leading-tight break-words max-w-full opacity-90\",\n                                        children: slide.specs\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\home\\\\AmazonStyleCarousel.tsx\",\n                                        lineNumber: 245,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\home\\\\AmazonStyleCarousel.tsx\",\n                                    lineNumber: 243,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\home\\\\AmazonStyleCarousel.tsx\",\n                                lineNumber: 242,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, index, true, {\n                        fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\home\\\\AmazonStyleCarousel.tsx\",\n                        lineNumber: 137,\n                        columnNumber: 11\n                    }, this);\n                })\n            }, void 0, false, {\n                fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\home\\\\AmazonStyleCarousel.tsx\",\n                lineNumber: 132,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                onClick: (e)=>{\n                    e.stopPropagation();\n                    goToPrevSlide();\n                },\n                className: \"absolute left-2 xs:left-4 top-1/2 -translate-y-1/2 w-8 h-8 xs:w-10 xs:h-10 items-center justify-center   bg-theme-accent-primary hover:bg-theme-accent-hover text-white rounded-full shadow-md z-10 transition-all duration-300   hover:shadow-lg hover:scale-110 hidden xs:inline-flex\",\n                \"aria-label\": \"Previous slide\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_ChevronRight_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                    className: \"h-4 w-4 xs:h-6 xs:w-6 text-white transition-transform duration-300 hover:-translate-x-0.5\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\home\\\\AmazonStyleCarousel.tsx\",\n                    lineNumber: 266,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\home\\\\AmazonStyleCarousel.tsx\",\n                lineNumber: 256,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                onClick: (e)=>{\n                    e.stopPropagation();\n                    goToNextSlide();\n                },\n                className: \"absolute right-2 xs:right-4 top-1/2 -translate-y-1/2 w-8 h-8 xs:w-10 xs:h-10 items-center justify-center   bg-theme-accent-primary hover:bg-theme-accent-hover text-white rounded-full shadow-md z-10 transition-all duration-300   hover:shadow-lg hover:scale-110 hidden xs:inline-flex\",\n                \"aria-label\": \"Next slide\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_ChevronRight_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                    className: \"h-4 w-4 xs:h-6 xs:w-6 text-white transition-transform duration-300 hover:translate-x-0.5\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\home\\\\AmazonStyleCarousel.tsx\",\n                    lineNumber: 279,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\home\\\\AmazonStyleCarousel.tsx\",\n                lineNumber: 269,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute bottom-2 xs:bottom-4 left-1/2 -translate-x-1/2 flex gap-1 xs:gap-2 z-10\",\n                children: slides.map((_, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: ()=>goToSlide(index),\n                        className: \"w-2 h-2 xs:w-3 xs:h-3 rounded-full transition-all duration-300 \".concat(index === currentSlide ? \"bg-theme-accent-secondary scale-110\" : \"bg-white/50 hover:bg-white/70 hover:scale-105\"),\n                        \"aria-label\": \"Go to slide \".concat(index + 1)\n                    }, index, false, {\n                        fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\home\\\\AmazonStyleCarousel.tsx\",\n                        lineNumber: 285,\n                        columnNumber: 11\n                    }, this))\n            }, void 0, false, {\n                fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\home\\\\AmazonStyleCarousel.tsx\",\n                lineNumber: 283,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\home\\\\AmazonStyleCarousel.tsx\",\n        lineNumber: 106,\n        columnNumber: 5\n    }, this);\n}\n_s(AmazonStyleCarousel, \"q+8ilV0we8mV9YlTA9aGx5Xg1EA=\");\n_c = AmazonStyleCarousel;\nvar _c;\n$RefreshReg$(_c, \"AmazonStyleCarousel\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/home/<USER>"));

/***/ })

});