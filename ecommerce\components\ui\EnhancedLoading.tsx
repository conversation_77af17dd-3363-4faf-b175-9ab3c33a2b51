"use client";

import React from 'react';
import { motion } from 'framer-motion';

interface EnhancedLoadingProps {
  variant?: 'hero' | 'section' | 'card' | 'minimal';
  className?: string;
}

export default function EnhancedLoading({ 
  variant = 'section', 
  className = '' 
}: EnhancedLoadingProps) {
  
  const getVariantConfig = () => {
    switch (variant) {
      case 'hero':
        return {
          container: 'w-full h-[400px] rounded-xl',
          elements: [
            { width: '100%', height: '200px', delay: 0 },
            { width: '80%', height: '40px', delay: 0.1 },
            { width: '60%', height: '30px', delay: 0.2 },
            { width: '40%', height: '50px', delay: 0.3 }
          ]
        };
      case 'section':
        return {
          container: 'w-full h-[300px] rounded-xl',
          elements: [
            { width: '100%', height: '150px', delay: 0 },
            { width: '70%', height: '30px', delay: 0.1 },
            { width: '50%', height: '20px', delay: 0.2 }
          ]
        };
      case 'card':
        return {
          container: 'w-full h-[200px] rounded-lg',
          elements: [
            { width: '100%', height: '120px', delay: 0 },
            { width: '80%', height: '20px', delay: 0.1 },
            { width: '60%', height: '15px', delay: 0.2 }
          ]
        };
      default:
        return {
          container: 'w-full h-[100px] rounded-lg',
          elements: [
            { width: '100%', height: '60px', delay: 0 },
            { width: '70%', height: '20px', delay: 0.1 }
          ]
        };
    }
  };

  const config = getVariantConfig();

  const shimmerVariants = {
    initial: { x: '-100%' },
    animate: { 
      x: '100%',
      transition: {
        duration: 1.5,
        repeat: Infinity,
        ease: 'easeInOut'
      }
    }
  };

  const pulseVariants = {
    initial: { opacity: 0.6 },
    animate: { 
      opacity: [0.6, 1, 0.6],
      transition: {
        duration: 2,
        repeat: Infinity,
        ease: 'easeInOut'
      }
    }
  };

  return (
    <div className={`glass-morphism p-4 sm:p-6 ${className}`}>
      <div className={`bg-gradient-to-r from-gray-200 via-gray-100 to-gray-200 ${config.container} relative overflow-hidden`}>
        {/* Shimmer effect */}
        <motion.div
          className="absolute inset-0 bg-gradient-to-r from-transparent via-white/60 to-transparent"
          variants={shimmerVariants}
          initial="initial"
          animate="animate"
        />
        
        {/* Content skeleton */}
        <div className="p-4 space-y-4">
          {config.elements.map((element, index) => (
            <motion.div
              key={index}
              className="bg-gray-300 rounded"
              style={{ 
                width: element.width, 
                height: element.height 
              }}
              variants={pulseVariants}
              initial="initial"
              animate="animate"
              transition={{ delay: element.delay }}
            />
          ))}
        </div>
      </div>
      
      {/* Floating particles for enhanced effect */}
      <div className="absolute inset-0 pointer-events-none">
        {[...Array(3)].map((_, i) => (
          <motion.div
            key={i}
            className="absolute w-2 h-2 bg-theme-accent-primary/30 rounded-full"
            style={{
              left: `${20 + i * 30}%`,
              top: `${30 + i * 20}%`
            }}
            animate={{
              y: [0, -10, 0],
              opacity: [0.3, 0.7, 0.3],
              scale: [1, 1.2, 1]
            }}
            transition={{
              duration: 2 + i * 0.5,
              repeat: Infinity,
              ease: 'easeInOut',
              delay: i * 0.3
            }}
          />
        ))}
      </div>
    </div>
  );
}

// Preset loading components
export function HeroLoading({ className = '' }: { className?: string }) {
  return <EnhancedLoading variant="hero" className={className} />;
}

export function SectionLoading({ className = '' }: { className?: string }) {
  return <EnhancedLoading variant="section" className={className} />;
}

export function CardLoading({ className = '' }: { className?: string }) {
  return <EnhancedLoading variant="card" className={className} />;
}
