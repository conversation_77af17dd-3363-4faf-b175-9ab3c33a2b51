"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./app/page.tsx":
/*!**********************!*\
  !*** ./app/page.tsx ***!
  \**********************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_use_toast__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../components/ui/use-toast */ \"(app-pages-browser)/./components/ui/use-toast.ts\");\n/* harmony import */ var _constant_urls__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../constant/urls */ \"(app-pages-browser)/./constant/urls.ts\");\n/* harmony import */ var _hooks_useApi__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../hooks/useApi */ \"(app-pages-browser)/./hooks/useApi.ts\");\n/* harmony import */ var _layout_MainHOF__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../layout/MainHOF */ \"(app-pages-browser)/./layout/MainHOF.tsx\");\n/* harmony import */ var swiper_css_autoplay__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! swiper/css/autoplay */ \"(app-pages-browser)/./node_modules/swiper/modules/autoplay.css\");\n/* harmony import */ var _components_ui_TrustIndicators__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/TrustIndicators */ \"(app-pages-browser)/./components/ui/TrustIndicators.tsx\");\n/* harmony import */ var _components_ClientOnly__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/ClientOnly */ \"(app-pages-browser)/./components/ClientOnly.tsx\");\n/* harmony import */ var _components_ui_AnimatedBackground__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/ui/AnimatedBackground */ \"(app-pages-browser)/./components/ui/AnimatedBackground.tsx\");\n/* harmony import */ var _components_ui_ScrollReveal__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/ui/ScrollReveal */ \"(app-pages-browser)/./components/ui/ScrollReveal.tsx\");\n/* harmony import */ var _components_ui_ParallaxContainer__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/components/ui/ParallaxContainer */ \"(app-pages-browser)/./components/ui/ParallaxContainer.tsx\");\n/* harmony import */ var _components_ui_EnhancedLoading__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/components/ui/EnhancedLoading */ \"(app-pages-browser)/./components/ui/EnhancedLoading.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n// Lazy load components outside of the component function\nconst HeroCarousel = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1___default().lazy(()=>__webpack_require__.e(/*! import() */ \"_app-pages-browser_components_home_hero-carousel_tsx\").then(__webpack_require__.bind(__webpack_require__, /*! @/components/home/<USER>/ \"(app-pages-browser)/./components/home/<USER>")));\n_c = HeroCarousel;\nconst ProductSection = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1___default().lazy(()=>__webpack_require__.e(/*! import() */ \"_app-pages-browser_components_home_ProductSection_tsx\").then(__webpack_require__.bind(__webpack_require__, /*! @/components/home/<USER>/ \"(app-pages-browser)/./components/home/<USER>")));\n_c1 = ProductSection;\nconst CategoryTabs = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1___default().lazy(()=>__webpack_require__.e(/*! import() */ \"_app-pages-browser_components_home_CategoryTabs_tsx\").then(__webpack_require__.bind(__webpack_require__, /*! @/components/home/<USER>/ \"(app-pages-browser)/./components/home/<USER>")));\n_c2 = CategoryTabs;\nconst ProductCategories = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1___default().lazy(()=>__webpack_require__.e(/*! import() */ \"_app-pages-browser_components_home_ProductCategories_tsx\").then(__webpack_require__.bind(__webpack_require__, /*! @/components/home/<USER>/ \"(app-pages-browser)/./components/home/<USER>")));\n_c3 = ProductCategories;\nconst Homepage = ()=>{\n    _s();\n    const { toast } = (0,_components_ui_use_toast__WEBPACK_IMPORTED_MODULE_2__.useToast)();\n    // Use a single API instance for all API calls\n    const { data, read } = (0,_hooks_useApi__WEBPACK_IMPORTED_MODULE_4__[\"default\"])(_constant_urls__WEBPACK_IMPORTED_MODULE_3__.MAIN_URL || '');\n    // Create state variables to store different data types\n    const [futureProduct, setFutureProduct] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [futureProductLoading, setFutureProductLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [popularProduct, setPopularProduct] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [popularProductLoading, setPopularProductLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [categories, setCategories] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [categoriesLoading, setCategoriesLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Use refs to track if data has been fetched\n    const initialDataFetchedRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(false);\n    const [categoryProducts, setCategoryProducts] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    // Track if category products have been fetched\n    const categoryProductsFetchedRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(false);\n    // Function to fetch products for each category - optimized to prevent continuous API calls\n    const fetchCategoryProducts = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"Homepage.useCallback[fetchCategoryProducts]\": async ()=>{\n            // Skip if categories aren't loaded yet\n            if (!Array.isArray(categories) || categories.length === 0) return;\n            // Skip if we've already fetched and have data\n            if (categoryProductsFetchedRef.current && categoryProducts.length > 0 && categoryProducts.every({\n                \"Homepage.useCallback[fetchCategoryProducts]\": (cat)=>!cat.loading\n            }[\"Homepage.useCallback[fetchCategoryProducts]\"])) {\n                return;\n            }\n            // Limit to first 6 categories to avoid overwhelming the page and reduce API calls\n            const limitedCategories = categories.slice(0, 6);\n            // Set initial loading state\n            if (categoryProducts.length === 0) {\n                const initialCategoryProducts = limitedCategories.map({\n                    \"Homepage.useCallback[fetchCategoryProducts].initialCategoryProducts\": (category)=>({\n                            category,\n                            products: [],\n                            loading: true\n                        })\n                }[\"Homepage.useCallback[fetchCategoryProducts].initialCategoryProducts\"]);\n                setCategoryProducts(initialCategoryProducts);\n            }\n            // Fetch products for each category\n            const promises = limitedCategories.map({\n                \"Homepage.useCallback[fetchCategoryProducts].promises\": async (category, index)=>{\n                    try {\n                        var _result_results;\n                        // Use the single read function from our consolidated API instance\n                        const result = await read(\"\".concat((0,_constant_urls__WEBPACK_IMPORTED_MODULE_3__.CATEGORIZE_PRODUCTS)(category.slug), \"?page_size=8\"));\n                        return {\n                            index,\n                            category,\n                            products: (result === null || result === void 0 ? void 0 : (_result_results = result.results) === null || _result_results === void 0 ? void 0 : _result_results.products) || [],\n                            success: true\n                        };\n                    } catch (error) {\n                        console.error(\"Error fetching products for \".concat(category.name, \":\"), error);\n                        return {\n                            index,\n                            category,\n                            products: [],\n                            success: false\n                        };\n                    }\n                }\n            }[\"Homepage.useCallback[fetchCategoryProducts].promises\"]);\n            // Wait for all promises to resolve\n            const results = await Promise.all(promises);\n            // Update state once with all results\n            setCategoryProducts({\n                \"Homepage.useCallback[fetchCategoryProducts]\": (prev)=>{\n                    // Start with previous state or empty array\n                    const newState = prev.length > 0 ? [\n                        ...prev\n                    ] : limitedCategories.map({\n                        \"Homepage.useCallback[fetchCategoryProducts]\": (cat)=>({\n                                category: cat,\n                                products: [],\n                                loading: true\n                            })\n                    }[\"Homepage.useCallback[fetchCategoryProducts]\"]);\n                    // Update with new results\n                    results.forEach({\n                        \"Homepage.useCallback[fetchCategoryProducts]\": (result)=>{\n                            if (newState[result.index]) {\n                                newState[result.index] = {\n                                    ...newState[result.index],\n                                    products: result.products,\n                                    loading: false\n                                };\n                            }\n                        }\n                    }[\"Homepage.useCallback[fetchCategoryProducts]\"]);\n                    // Mark as fetched\n                    categoryProductsFetchedRef.current = true;\n                    return newState;\n                }\n            }[\"Homepage.useCallback[fetchCategoryProducts]\"]);\n        }\n    }[\"Homepage.useCallback[fetchCategoryProducts]\"], [\n        categories,\n        read\n    ]);\n    // Load all initial data with a single useEffect to reduce renders and prevent continuous API calls\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Homepage.useEffect\": ()=>{\n            // Skip if we've already loaded the data\n            if (initialDataFetchedRef.current) return;\n            // Create a flag to track if the component is still mounted\n            let isMounted = true;\n            const loadInitialData = {\n                \"Homepage.useEffect.loadInitialData\": async ()=>{\n                    try {\n                        setCategoriesLoading(true);\n                        setFutureProductLoading(true);\n                        setPopularProductLoading(true);\n                        // Load categories first\n                        const categoriesResult = await read(_constant_urls__WEBPACK_IMPORTED_MODULE_3__.CATEGORIES);\n                        // Only continue if component is still mounted\n                        if (!isMounted) return;\n                        // Update categories state\n                        if (categoriesResult) {\n                            setCategories(categoriesResult);\n                        }\n                        setCategoriesLoading(false);\n                        // Load featured and popular products in parallel\n                        const [featuredResult, popularResult] = await Promise.all([\n                            read(_constant_urls__WEBPACK_IMPORTED_MODULE_3__.FUTURED_PRODUCTS),\n                            read(_constant_urls__WEBPACK_IMPORTED_MODULE_3__.PRODUCTS + '?page_size=10') // Fetch 10 products for the Discover section\n                        ]);\n                        if (!isMounted) return;\n                        // Update featured products state\n                        if (featuredResult) {\n                            setFutureProduct(featuredResult);\n                        }\n                        // Update popular products state\n                        if (popularResult) {\n                            setPopularProduct(popularResult);\n                        }\n                        // Mark as fetched to prevent duplicate API calls\n                        initialDataFetchedRef.current = true;\n                        setFutureProductLoading(false);\n                        setPopularProductLoading(false);\n                    } catch (error) {\n                        console.error(\"Error loading initial data:\", error);\n                        setCategoriesLoading(false);\n                        setFutureProductLoading(false);\n                        setPopularProductLoading(false);\n                    }\n                }\n            }[\"Homepage.useEffect.loadInitialData\"];\n            loadInitialData();\n            // Cleanup function to prevent state updates after unmount\n            return ({\n                \"Homepage.useEffect\": ()=>{\n                    isMounted = false;\n                }\n            })[\"Homepage.useEffect\"];\n        }\n    }[\"Homepage.useEffect\"], [\n        read\n    ]);\n    // Fetch products for each category when categories are loaded\n    // This useEffect now depends only on categories and fetchCategoryProducts\n    // It will only run when categories change, not on every render\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Homepage.useEffect\": ()=>{\n            if (categories && categories.length > 0 && !categoryProductsFetchedRef.current) {\n                fetchCategoryProducts();\n            }\n        }\n    }[\"Homepage.useEffect\"], [\n        fetchCategoryProducts,\n        categories\n    ]);\n    // We no longer need featuredProductsForHero since we're using HeroCarousel\n    // Get featured products for product section - memoized to prevent recalculations\n    const featuredProducts = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)({\n        \"Homepage.useMemo[featuredProducts]\": ()=>{\n            if (futureProduct && Array.isArray(futureProduct)) {\n                return futureProduct;\n            } else if (futureProduct && futureProduct.results && Array.isArray(futureProduct.results)) {\n                return futureProduct.results;\n            } else if (futureProduct && futureProduct.products && Array.isArray(futureProduct.products)) {\n                return futureProduct.products;\n            }\n            // Return empty array if no products are available - no more static fallback\n            return [];\n        }\n    }[\"Homepage.useMemo[featuredProducts]\"], [\n        futureProduct\n    ]);\n    // Get popular products - memoized to prevent recalculations\n    const popularProducts = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)({\n        \"Homepage.useMemo[popularProducts]\": ()=>{\n            // First check our dedicated popularProduct state\n            if (popularProduct && Array.isArray(popularProduct)) {\n                return popularProduct;\n            } else if (popularProduct && popularProduct.results && Array.isArray(popularProduct.results)) {\n                return popularProduct.results;\n            } else if (popularProduct && popularProduct.products && Array.isArray(popularProduct.products)) {\n                return popularProduct.products;\n            }\n            // Fallback to data from the main API call if popularProduct isn't available\n            if (data && Array.isArray(data)) {\n                return data;\n            } else if (data && data.results && Array.isArray(data.results)) {\n                return data.results;\n            } else if (data && data.products && Array.isArray(data.products)) {\n                return data.products;\n            }\n            // Return empty array if no products are available - no more static fallback\n            return [];\n        }\n    }[\"Homepage.useMemo[popularProducts]\"], [\n        popularProduct,\n        data\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_AnimatedBackground__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n        variant: \"hero\",\n        className: \"bg-gradient-vibrant w-full min-h-screen\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_layout_MainHOF__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"min-h-screen flex flex-col w-full\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_ScrollReveal__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                        animation: \"slideDown\",\n                        delay: 0.1,\n                        className: \"relative z-20 mt-2\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"glass-morphism rounded-2xl mx-2 sm:mx-4 p-2 hover-lift\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ProductCategories, {\n                                categories: categories !== null && categories !== void 0 ? categories : [],\n                                variant: \"navigation\",\n                                showTitle: false,\n                                showViewAll: false,\n                                maxCategories: 12,\n                                accentColor: \"primary\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Triumph\\\\ecommerce\\\\app\\\\page.tsx\",\n                                lineNumber: 276,\n                                columnNumber: 15\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Triumph\\\\ecommerce\\\\app\\\\page.tsx\",\n                            lineNumber: 275,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Triumph\\\\ecommerce\\\\app\\\\page.tsx\",\n                        lineNumber: 270,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ClientOnly__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((react__WEBPACK_IMPORTED_MODULE_1___default().Suspense), {\n                            fallback: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-full space-y-8\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_EnhancedLoading__WEBPACK_IMPORTED_MODULE_12__.HeroLoading, {\n                                        className: \"mx-2 sm:mx-4\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Triumph\\\\ecommerce\\\\app\\\\page.tsx\",\n                                        lineNumber: 291,\n                                        columnNumber: 17\n                                    }, void 0),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_EnhancedLoading__WEBPACK_IMPORTED_MODULE_12__.SectionLoading, {\n                                        className: \"mx-2 sm:mx-4\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Triumph\\\\ecommerce\\\\app\\\\page.tsx\",\n                                        lineNumber: 292,\n                                        columnNumber: 17\n                                    }, void 0),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_EnhancedLoading__WEBPACK_IMPORTED_MODULE_12__.SectionLoading, {\n                                        className: \"mx-2 sm:mx-4\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Triumph\\\\ecommerce\\\\app\\\\page.tsx\",\n                                        lineNumber: 293,\n                                        columnNumber: 17\n                                    }, void 0)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Triumph\\\\ecommerce\\\\app\\\\page.tsx\",\n                                lineNumber: 290,\n                                columnNumber: 15\n                            }, void 0),\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                                    className: \"relative w-full\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_ParallaxContainer__WEBPACK_IMPORTED_MODULE_11__.ParallaxFloat, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_ScrollReveal__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                animation: \"fadeIn\",\n                                                delay: 0.2,\n                                                className: \"hover-lift\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(HeroCarousel, {}, void 0, false, {\n                                                    fileName: \"D:\\\\Triumph\\\\ecommerce\\\\app\\\\page.tsx\",\n                                                    lineNumber: 300,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Triumph\\\\ecommerce\\\\app\\\\page.tsx\",\n                                                lineNumber: 299,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Triumph\\\\ecommerce\\\\app\\\\page.tsx\",\n                                            lineNumber: 298,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_ScrollReveal__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                            animation: \"slideUp\",\n                                            delay: 0.4,\n                                            className: \"relative z-10 mt-6 mb-8\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_TrustIndicators__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {}, void 0, false, {\n                                                fileName: \"D:\\\\Triumph\\\\ecommerce\\\\app\\\\page.tsx\",\n                                                lineNumber: 304,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Triumph\\\\ecommerce\\\\app\\\\page.tsx\",\n                                            lineNumber: 303,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Triumph\\\\ecommerce\\\\app\\\\page.tsx\",\n                                    lineNumber: 297,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_ParallaxContainer__WEBPACK_IMPORTED_MODULE_11__.ParallaxFade, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_ScrollReveal__WEBPACK_IMPORTED_MODULE_10__.GlassReveal, {\n                                        delay: 0.6,\n                                        className: \"bg-gradient-to-r from-white/5 to-white/10\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ProductSection, {\n                                            title: \"Featured Products\",\n                                            subtitle: \"Discover our handpicked selection of premium products\",\n                                            products: featuredProducts,\n                                            loading: futureProductLoading,\n                                            viewAllLink: \"/shop\",\n                                            accentColor: \"primary\",\n                                            columns: {\n                                                xs: 2,\n                                                sm: 2,\n                                                md: 3,\n                                                lg: 4,\n                                                xl: 4,\n                                                \"2xl\": 5\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Triumph\\\\ecommerce\\\\app\\\\page.tsx\",\n                                            lineNumber: 311,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Triumph\\\\ecommerce\\\\app\\\\page.tsx\",\n                                        lineNumber: 310,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Triumph\\\\ecommerce\\\\app\\\\page.tsx\",\n                                    lineNumber: 309,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_ParallaxContainer__WEBPACK_IMPORTED_MODULE_11__.ParallaxFade, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_ScrollReveal__WEBPACK_IMPORTED_MODULE_10__.GlassReveal, {\n                                        delay: 0.8,\n                                        className: \"bg-gradient-to-l from-white/5 to-white/10\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ProductSection, {\n                                            title: \"Discover Products\",\n                                            subtitle: \"Explore our most popular items\",\n                                            products: popularProducts,\n                                            loading: popularProductLoading,\n                                            viewAllLink: \"/shop\",\n                                            accentColor: \"secondary\",\n                                            columns: {\n                                                xs: 2,\n                                                sm: 2,\n                                                md: 3,\n                                                lg: 4,\n                                                xl: 4,\n                                                \"2xl\": 5\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Triumph\\\\ecommerce\\\\app\\\\page.tsx\",\n                                            lineNumber: 333,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Triumph\\\\ecommerce\\\\app\\\\page.tsx\",\n                                        lineNumber: 332,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Triumph\\\\ecommerce\\\\app\\\\page.tsx\",\n                                    lineNumber: 331,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_ScrollReveal__WEBPACK_IMPORTED_MODULE_10__.GlassReveal, {\n                                    delay: 1.0,\n                                    className: \"bg-gradient-to-r from-white/5 to-white/10\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ProductCategories, {\n                                        title: \"Shop by Category\",\n                                        subtitle: \"Browse our collection by category\",\n                                        categories: categories || [],\n                                        accentColor: \"tertiary\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Triumph\\\\ecommerce\\\\app\\\\page.tsx\",\n                                        lineNumber: 354,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Triumph\\\\ecommerce\\\\app\\\\page.tsx\",\n                                    lineNumber: 353,\n                                    columnNumber: 13\n                                }, undefined),\n                                categoryProducts.some((cat)=>cat.products && cat.products.length > 0) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_ScrollReveal__WEBPACK_IMPORTED_MODULE_10__.GlassReveal, {\n                                    delay: 1.2,\n                                    className: \"bg-gradient-to-l from-white/5 to-white/10\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CategoryTabs, {\n                                        title: \"Browse Products by Category\",\n                                        subtitle: \"Filter products by your favorite categories\",\n                                        categories: categories || [],\n                                        categoryProducts: categoryProducts.filter((cat)=>cat.products && cat.products.length > 0),\n                                        accentColor: \"primary\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Triumph\\\\ecommerce\\\\app\\\\page.tsx\",\n                                        lineNumber: 365,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Triumph\\\\ecommerce\\\\app\\\\page.tsx\",\n                                    lineNumber: 364,\n                                    columnNumber: 15\n                                }, undefined),\n                                categoryProducts.map((categoryData, categoryIndex)=>// Only show categories with at least 4 products\n                                    Array.isArray(categoryData.products) && categoryData.products.length >= 4 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_ParallaxContainer__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                        speed: 0.1,\n                                        direction: \"up\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_ScrollReveal__WEBPACK_IMPORTED_MODULE_10__.GlassReveal, {\n                                            delay: 1.4 + categoryIndex * 0.2,\n                                            className: \"bg-gradient-to-br from-white/5 to-white/8 hover-lift\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ProductSection, {\n                                                title: categoryData.category.name,\n                                                products: categoryData.products,\n                                                loading: categoryData.loading,\n                                                viewAllLink: \"/shop?category=\".concat(categoryData.category.slug),\n                                                accentColor: categoryIndex % 3 === 0 ? \"primary\" : categoryIndex % 3 === 1 ? \"secondary\" : \"tertiary\",\n                                                columns: {\n                                                    xs: 2,\n                                                    sm: 2,\n                                                    md: 3,\n                                                    lg: 4,\n                                                    xl: 4,\n                                                    \"2xl\": 5\n                                                }\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Triumph\\\\ecommerce\\\\app\\\\page.tsx\",\n                                                lineNumber: 388,\n                                                columnNumber: 21\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Triumph\\\\ecommerce\\\\app\\\\page.tsx\",\n                                            lineNumber: 384,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    }, categoryData.category.id, false, {\n                                        fileName: \"D:\\\\Triumph\\\\ecommerce\\\\app\\\\page.tsx\",\n                                        lineNumber: 379,\n                                        columnNumber: 17\n                                    }, undefined) : null)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Triumph\\\\ecommerce\\\\app\\\\page.tsx\",\n                            lineNumber: 289,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Triumph\\\\ecommerce\\\\app\\\\page.tsx\",\n                        lineNumber: 288,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\Triumph\\\\ecommerce\\\\app\\\\page.tsx\",\n                lineNumber: 268,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"D:\\\\Triumph\\\\ecommerce\\\\app\\\\page.tsx\",\n            lineNumber: 267,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"D:\\\\Triumph\\\\ecommerce\\\\app\\\\page.tsx\",\n        lineNumber: 264,\n        columnNumber: 5\n    }, undefined);\n};\n_s(Homepage, \"t1NL8oCH1x/iqJpW5zHQdihbFlM=\", false, function() {\n    return [\n        _components_ui_use_toast__WEBPACK_IMPORTED_MODULE_2__.useToast,\n        _hooks_useApi__WEBPACK_IMPORTED_MODULE_4__[\"default\"]\n    ];\n});\n_c4 = Homepage;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Homepage);\nvar _c, _c1, _c2, _c3, _c4;\n$RefreshReg$(_c, \"HeroCarousel\");\n$RefreshReg$(_c1, \"ProductSection\");\n$RefreshReg$(_c2, \"CategoryTabs\");\n$RefreshReg$(_c3, \"ProductCategories\");\n$RefreshReg$(_c4, \"Homepage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/page.tsx\n"));

/***/ })

});