"use client";
import React, { Suspense } from "react";
import ProductCardLoading from "@/components/ui/loading/ProductCardLoading";
import Product from "../components/product/Product";
import { Button } from "../components/ui/button";
import { Card, CardContent } from "../components/ui/card";
import {
  Carousel,
  CarouselContent,
  CarouselItem,
  CarouselNext,
  CarouselPrevious,
} from "../components/ui/carousel";
import { Input } from "../components/ui/input";
import { useToast } from "../components/ui/use-toast";
import {
  CATEGORIES,
  CATEGORIZE_PRODUCTS,
  FUTURED_PRODUCTS,
  MAIN_URL,
  PRODUCTS,
} from "../constant/urls";
import useApi from "../hooks/useApi";
import MainHOF from "../layout/MainHOF";
import { ChevronRight, ShoppingCart, User, Search, Star } from "lucide-react";
import Link from "next/link";
import { useCallback, useEffect, useState, useMemo, useRef } from "react";
import { Swiper, SwiperSlide } from "swiper/react";
import { Autoplay } from "swiper/modules";
import "swiper/css/autoplay";
import TrustIndicators from "@/components/ui/TrustIndicators";
import PromoBanner from "@/components/home/<USER>";
import ClientOnly from "@/components/ClientOnly";
import AnimatedBackground from "@/components/ui/AnimatedBackground";
import ScrollReveal, { GlassReveal, SlideUpReveal, StaggeredReveal } from "@/components/ui/ScrollReveal";
import ParallaxContainer, { ParallaxFloat, ParallaxFade } from "@/components/ui/ParallaxContainer";
import { HeroLoading, SectionLoading } from "@/components/ui/EnhancedLoading";
import { VibrantGradient } from "@/components/ui/GradientOverlay";

// Lazy load components outside of the component function
const HeroCarousel = React.lazy(() => import('@/components/home/<USER>'));
const ProductSection = React.lazy(() => import('@/components/home/<USER>'));
const CategoryTabs = React.lazy(() => import('@/components/home/<USER>'));
const ProductCategories = React.lazy(() => import('@/components/home/<USER>'));

// Define types
interface CategoryProducts {
  category: any;
  products: any[];
  loading: boolean;
}
const Homepage = () => {
  const { toast } = useToast();
  // Use a single API instance for all API calls
  const { data, read }: any = useApi(MAIN_URL || '');

  // Create state variables to store different data types
  const [futureProduct, setFutureProduct] = useState<any>(null);
  const [futureProductLoading, setFutureProductLoading] = useState<boolean>(false);
  const [popularProduct, setPopularProduct] = useState<any>(null);
  const [popularProductLoading, setPopularProductLoading] = useState<boolean>(false);
  const [categories, setCategories] = useState<any[]>([]);
  const [categoriesLoading, setCategoriesLoading] = useState<boolean>(false);

  // Use refs to track if data has been fetched
  const initialDataFetchedRef = useRef<boolean>(false);

  const [categoryProducts, setCategoryProducts] = useState<CategoryProducts[]>([]);

  // Track if category products have been fetched
  const categoryProductsFetchedRef = useRef<boolean>(false);

  // Function to fetch products for each category - optimized to prevent continuous API calls
  const fetchCategoryProducts = useCallback(async () => {
    // Skip if categories aren't loaded yet
    if (!Array.isArray(categories) || categories.length === 0) return;

    // Skip if we've already fetched and have data
    if (categoryProductsFetchedRef.current &&
        categoryProducts.length > 0 &&
        categoryProducts.every(cat => !cat.loading)) {
      return;
    }

    // Limit to first 6 categories to avoid overwhelming the page and reduce API calls
    const limitedCategories = categories.slice(0, 6);

    // Set initial loading state
    if (categoryProducts.length === 0) {
      const initialCategoryProducts = limitedCategories.map((category: any) => ({
        category,
        products: [],
        loading: true,
      }));
      setCategoryProducts(initialCategoryProducts);
    }

    // Fetch products for each category
    const promises = limitedCategories.map(async (category, index) => {
      try {
        // Use the single read function from our consolidated API instance
        const result = await read(
          `${CATEGORIZE_PRODUCTS(category.slug)}?page_size=8`
        );

        return {
          index,
          category,
          products: result?.results?.products || [],
          success: true
        };
      } catch (error) {
        console.error(`Error fetching products for ${category.name}:`, error);
        return {
          index,
          category,
          products: [],
          success: false
        };
      }
    });

    // Wait for all promises to resolve
    const results = await Promise.all(promises);

    // Update state once with all results
    setCategoryProducts(prev => {
      // Start with previous state or empty array
      const newState = prev.length > 0 ? [...prev] :
        limitedCategories.map(cat => ({ category: cat, products: [], loading: true }));

      // Update with new results
      results.forEach(result => {
        if (newState[result.index]) {
          newState[result.index] = {
            ...newState[result.index],
            products: result.products,
            loading: false,
          };
        }
      });

      // Mark as fetched
      categoryProductsFetchedRef.current = true;

      return newState;
    });
  }, [categories, read]);

  // Load all initial data with a single useEffect to reduce renders and prevent continuous API calls
  useEffect(() => {
    // Skip if we've already loaded the data
    if (initialDataFetchedRef.current) return;

    // Create a flag to track if the component is still mounted
    let isMounted = true;

    const loadInitialData = async () => {
      try {
        setCategoriesLoading(true);
        setFutureProductLoading(true);
        setPopularProductLoading(true);

        // Load categories first
        const categoriesResult = await read(CATEGORIES);

        // Only continue if component is still mounted
        if (!isMounted) return;

        // Update categories state
        if (categoriesResult) {
          setCategories(categoriesResult);
        }

        setCategoriesLoading(false);

        // Load featured and popular products in parallel
        const [featuredResult, popularResult] = await Promise.all([
          read(FUTURED_PRODUCTS),
          read(PRODUCTS + '?page_size=10') // Fetch 10 products for the Discover section
        ]);

        if (!isMounted) return;

        // Update featured products state
        if (featuredResult) {
          setFutureProduct(featuredResult);
        }

        // Update popular products state
        if (popularResult) {
          setPopularProduct(popularResult);
        }

        // Mark as fetched to prevent duplicate API calls
        initialDataFetchedRef.current = true;
        setFutureProductLoading(false);
        setPopularProductLoading(false);
      } catch (error) {
        console.error("Error loading initial data:", error);
        setCategoriesLoading(false);
        setFutureProductLoading(false);
        setPopularProductLoading(false);
      }
    };

    loadInitialData();

    // Cleanup function to prevent state updates after unmount
    return () => {
      isMounted = false;
    };
  }, [read]);

  // Fetch products for each category when categories are loaded
  // This useEffect now depends only on categories and fetchCategoryProducts
  // It will only run when categories change, not on every render
  useEffect(() => {
    if (categories && categories.length > 0 && !categoryProductsFetchedRef.current) {
      fetchCategoryProducts();
    }
  }, [fetchCategoryProducts, categories]);


  // We no longer need featuredProductsForHero since we're using HeroCarousel

  // Get featured products for product section - memoized to prevent recalculations
  const featuredProducts = useMemo(() => {
    if (futureProduct && Array.isArray(futureProduct)) {
      return futureProduct;
    } else if (futureProduct && futureProduct.results && Array.isArray(futureProduct.results)) {
      return futureProduct.results;
    } else if (futureProduct && futureProduct.products && Array.isArray(futureProduct.products)) {
      return futureProduct.products;
    }
    // Return empty array if no products are available - no more static fallback
    return [];
  }, [futureProduct]);

  // Get popular products - memoized to prevent recalculations
  const popularProducts = useMemo(() => {
    // First check our dedicated popularProduct state
    if (popularProduct && Array.isArray(popularProduct)) {
      return popularProduct;
    } else if (popularProduct && popularProduct.results && Array.isArray(popularProduct.results)) {
      return popularProduct.results;
    } else if (popularProduct && popularProduct.products && Array.isArray(popularProduct.products)) {
      return popularProduct.products;
    }

    // Fallback to data from the main API call if popularProduct isn't available
    if (data && Array.isArray(data)) {
      return data;
    } else if (data && data.results && Array.isArray(data.results)) {
      return data.results;
    } else if (data && data.products && Array.isArray(data.products)) {
      return data.products;
    }

    // Return empty array if no products are available - no more static fallback
    return [];
  }, [popularProduct, data]);

  return (
    <div
      className="bg-gradient-vibrant w-full min-h-screen"
      style={{
        background: 'linear-gradient(135deg, #ff6b6b 0%, #4ecdc4 20%, #45b7d1 40%, #96ceb4 60%, #feca57 80%, #ff9ff3 100%)',
        backgroundSize: '400% 400%',
        animation: 'gradient-shift 15s ease infinite'
      }}
    >
      <AnimatedBackground variant="hero" className="w-full min-h-screen">{/* Enhanced animated background with Framer Motion */}

      {/* <PromoBanner /> */}
      <MainHOF>
        <div className="min-h-screen flex flex-col w-full">
          {/* Navigation - Enhanced with scroll reveal and glass morphism */}
          <ScrollReveal
            animation="slideDown"
            delay={0.1}
            className="relative z-20 mt-2"
          >
            <div className="glass-morphism rounded-2xl mx-2 sm:mx-4 p-2 hover-lift">
              <ProductCategories
                categories={categories ?? []}
                variant="navigation"
                showTitle={false}
                showViewAll={false}
                maxCategories={12}
                accentColor="primary"
              />
            </div>
          </ScrollReveal>

          {/* Use a single Suspense boundary for all lazy-loaded components */}
          <ClientOnly>
            <React.Suspense fallback={
              <div className="w-full space-y-8">
                <HeroLoading className="mx-2 sm:mx-4" />
                <SectionLoading className="mx-2 sm:mx-4" />
                <SectionLoading className="mx-2 sm:mx-4" />
              </div>
            }>
              {/* Hero Section with Featured Products - Enhanced with parallax and animations */}
              <section className="relative w-full">
                <ParallaxFloat>
                  <ScrollReveal animation="fadeIn" delay={0.2} className="hover-lift">
                    <HeroCarousel />
                  </ScrollReveal>
                </ParallaxFloat>
                <ScrollReveal animation="slideUp" delay={0.4} className="relative z-10 mt-6 mb-8">
                  <TrustIndicators />
                </ScrollReveal>
              </section>

            {/* Featured Products Section - Enhanced with glass morphism and parallax */}
            <ParallaxFade>
              <GlassReveal delay={0.6} className="bg-gradient-to-r from-white/5 to-white/10">
                <ProductSection
                  title="Featured Products"
                  subtitle="Discover our handpicked selection of premium products"
                  products={featuredProducts}
                  loading={futureProductLoading}
                  viewAllLink="/shop"
                  accentColor="primary"
                  columns={{
                    xs: 2,
                    sm: 2,
                    md: 3,
                    lg: 4,
                    xl: 4,
                    "2xl": 5
                  }}
                />
              </GlassReveal>
            </ParallaxFade>

            {/* Popular Products Section - Enhanced with glass morphism and parallax */}
            <ParallaxFade>
              <GlassReveal delay={0.8} className="bg-gradient-to-l from-white/5 to-white/10">
                <ProductSection
                  title="Discover Products"
                  subtitle="Explore our most popular items"
                  products={popularProducts}
                  loading={popularProductLoading}
                  viewAllLink="/shop"
                  accentColor="secondary"
                  columns={{
                    xs: 2,
                    sm: 2,
                    md: 3,
                    lg: 4,
                    xl: 4,
                    "2xl": 5
                  }}
                />
              </GlassReveal>
            </ParallaxFade>

            {/* Product Categories Grid - Enhanced with glass morphism */}
            <GlassReveal delay={1.0} className="bg-gradient-to-r from-white/5 to-white/10">
              <ProductCategories
                title="Shop by Category"
                subtitle="Browse our collection by category"
                categories={categories || []}
                accentColor="tertiary"
              />
            </GlassReveal>

            {/* Category Tabs Section - Enhanced with glass morphism */}
            {categoryProducts.some(cat => cat.products && cat.products.length > 0) && (
              <GlassReveal delay={1.2} className="bg-gradient-to-l from-white/5 to-white/10">
                <CategoryTabs
                  title="Browse Products by Category"
                  subtitle="Filter products by your favorite categories"
                  categories={categories || []}
                  categoryProducts={categoryProducts.filter(cat => cat.products && cat.products.length > 0)}
                  accentColor="primary"
                />
              </GlassReveal>
            )}

            {/* Individual Category Sections - Enhanced with staggered glass morphism */}
            {categoryProducts.map((categoryData, categoryIndex) => (
              // Only show categories with at least 4 products
              Array.isArray(categoryData.products) && categoryData.products.length >= 4 ? (
                <ParallaxContainer
                  key={categoryData.category.id}
                  speed={0.1}
                  direction="up"
                >
                  <GlassReveal
                    delay={1.4 + (categoryIndex * 0.2)}
                    className="bg-gradient-to-br from-white/5 to-white/8 hover-lift"
                  >
                    <ProductSection
                      title={categoryData.category.name}
                      products={categoryData.products}
                      loading={categoryData.loading}
                      viewAllLink={`/shop?category=${categoryData.category.slug}`}
                      accentColor={categoryIndex % 3 === 0 ? "primary" : categoryIndex % 3 === 1 ? "secondary" : "tertiary"}
                      columns={{
                        xs: 2,
                        sm: 2,
                        md: 3,
                        lg: 4,
                        xl: 4,
                        "2xl": 5
                      }}
                    />
                  </GlassReveal>
                </ParallaxContainer>
              ) : null
            ))}
            </React.Suspense>
          </ClientOnly>
        </div>
      </MainHOF>
      </AnimatedBackground>
    </div>
  );
};

export default Homepage;
